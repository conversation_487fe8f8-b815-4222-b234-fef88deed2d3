# 元萝卜Xposed模块使用指南

## 项目概述

本项目是一个标准的Xposed模块，专门用于Hook元萝卜应用(`top.bienvenido.saas.i18n`)。项目已完成从自定义Xposed实现到标准XposedBridgeApi-82的迁移。

## 主要特性

- ✅ 使用标准XposedBridgeApi-82.jar
- ✅ 完整的Xposed模块配置
- ✅ 针对元萝卜应用的专门Hook实现
- ✅ 支持Activity生命周期监控
- ✅ 网络请求拦截
- ✅ 加密操作监控
- ✅ 应用验证Hook

## 项目结构

```
app/
├── libs/
│   └── XposedBridgeApi-82.jar          # 标准Xposed API
├── src/main/
│   ├── assets/
│   │   └── xposed_init                  # Xposed模块入口配置
│   ├── java/com/example/ypb_xp/
│   │   ├── XposedModule.java            # 主模块入口类
│   │   └── hooks/
│   │       └── YuanLuoBoHook.java       # 元萝卜应用Hook实现
│   ├── res/values/
│   │   └── arrays.xml                   # Xposed作用范围配置
│   └── AndroidManifest.xml              # 包含Xposed模块元数据
└── build.gradle.kts                     # 项目依赖配置
```

## 核心文件说明

### 1. XposedModule.java
主模块入口类，实现`IXposedHookLoadPackage`接口，负责：
- 包加载监听
- 根据包名分发Hook逻辑
- 调用具体的Hook实现

### 2. YuanLuoBoHook.java
元萝卜应用专用Hook实现，包含：
- Activity生命周期Hook
- 网络请求监控
- 加密操作拦截
- 应用验证绕过

### 3. AndroidManifest.xml配置
```xml
<!-- Xposed模块配置 -->
<meta-data android:name="xposedmodule" android:value="true" />
<meta-data android:name="xposeddescription" android:value="元萝卜Xposed模块 - 使用标准XposedBridgeApi-82" />
<meta-data android:name="xposedminversion" android:value="82" />
<meta-data android:name="xposedscope" android:resource="@array/xposed_scope" />
```

### 4. 作用范围配置(arrays.xml)
```xml
<string-array name="xposed_scope">
    <item>android</item>                    <!-- 系统框架 -->
    <item>com.android.settings</item>       <!-- 系统设置 -->
    <item>top.bienvenido.saas.i18n</item>   <!-- 目标应用：元萝卜 -->
    <item>com.android.systemui</item>       <!-- 系统UI -->
    <item>com.android.packageinstaller</item> <!-- 包管理器 -->
</string-array>
```

## 构建和安装

### 1. 构建APK
```bash
./gradlew build
```

### 2. 安装模块
1. 将生成的APK安装到设备
2. 在Xposed Installer中启用模块
3. 重启设备使模块生效

### 3. 验证安装
- 检查Xposed日志中是否有模块加载信息
- 运行元萝卜应用，观察Hook日志输出

## Hook功能说明

### Activity生命周期监控
- Hook `Activity.onCreate()` 和 `Activity.onResume()`
- 记录Activity创建和恢复事件
- 用于分析应用界面流程

### 网络请求拦截
- Hook OkHttp客户端请求
- Hook HttpURLConnection连接
- 记录网络请求详情

### 加密操作监控
- Hook `Cipher.doFinal()` 方法
- 监控加密/解密操作
- 记录数据长度信息

### 应用验证Hook
- Hook `PackageManager.getPackageInfo()`
- 监控包信息查询
- 可用于绕过应用完整性检查

## 日志查看

使用以下命令查看Xposed日志：
```bash
adb logcat | grep "YpbXpModule\|YuanLuoBoHook"
```

## 注意事项

1. **权限要求**：模块需要系统级权限，建议在Root设备上使用
2. **兼容性**：确保Xposed框架版本≥82
3. **目标应用**：主要针对元萝卜应用(`top.bienvenido.saas.i18n`)
4. **安全性**：仅用于学习和研究目的

## 故障排除

### 模块未生效
1. 检查Xposed框架是否正确安装
2. 确认模块已在Xposed Installer中启用
3. 重启设备后再次测试

### Hook失败
1. 检查目标应用版本是否匹配
2. 查看Xposed日志中的错误信息
3. 确认Hook的类和方法是否存在

### 编译错误
1. 确保XposedBridgeApi-82.jar文件存在
2. 检查Gradle配置是否正确
3. 清理项目后重新构建

## 开发扩展

如需添加新的Hook功能：

1. 在`YuanLuoBoHook.java`中添加新的Hook方法
2. 在`initHooks()`中调用新方法
3. 重新编译和安装模块

示例Hook代码：
```java
private static void hookCustomMethod(XC_LoadPackage.LoadPackageParam lpparam) {
    try {
        Class<?> targetClass = XposedHelpers.findClass(
            "com.example.TargetClass", lpparam.classLoader);
        
        XposedHelpers.findAndHookMethod(targetClass, "targetMethod",
            String.class, new XC_MethodHook() {
            @Override
            protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                // Hook逻辑
            }
        });
    } catch (Throwable e) {
        XposedBridge.log("[YuanLuoBoHook] Hook失败: " + e.getMessage());
    }
}
```

---

**免责声明**：本模块仅供学习和研究使用，请遵守相关法律法规。
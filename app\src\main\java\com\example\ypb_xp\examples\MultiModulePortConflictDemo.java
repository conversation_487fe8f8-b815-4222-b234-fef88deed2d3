package com.example.ypb_xp.examples;

import android.util.Log;
import com.example.ypb_xp.utils.GlobalPortCoordinator;
import com.example.ypb_xp.utils.ModulePortRangeManager;
import com.example.ypb_xp.utils.MultiModuleSafeNanoHTTPD;
import de.robv.android.xposed.IXposedHookLoadPackage;
import de.robv.android.xposed.XC_MethodHook;
import de.robv.android.xposed.XposedHelpers;
import de.robv.android.xposed.callbacks.XC_LoadPackage;
import fi.iki.elonen.NanoHTTPD;
import java.util.Map;

/**
 * 多模块端口冲突解决方案演示
 * 
 * 此示例展示如何在多模块环境下使用MultiModuleSafeNanoHTTPD
 * 来避免端口冲突问题，特别是针对淘宝应用的Hook场景
 * 
 * 解决的问题：
 * - com.call.taobao.spdy.HookLoader 端口冲突
 * - com.bypass.ali.spdy.HookLoader 端口冲突
 * - 多个模块同时运行时的端口协调
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class MultiModulePortConflictDemo implements IXposedHookLoadPackage {
    private static final String TAG = "MultiModulePortConflictDemo";
    
    // HTTP服务器实例
    private MultiModuleSafeNanoHTTPD httpServer;
    
    // 模块标识
    private static final String MODULE_NAME = "com.example.ypb_xp.demo";
    
    @Override
    public void handleLoadPackage(XC_LoadPackage.LoadPackageParam lpparam) throws Throwable {
        // 只在淘宝应用中激活
        if (!lpparam.packageName.equals("com.taobao.taobao")) {
            return;
        }
        
        Log.i(TAG, "🎯 开始在淘宝应用中应用多模块端口冲突解决方案");
        
        // 初始化端口管理
        initializePortManagement();
        
        // 启动HTTP服务器
        startHttpServerSafely();
        
        // Hook淘宝应用的关键方法
        hookTaobaoMethods(lpparam);
        
        // Hook其他模块的NanoHTTPD使用，防止端口冲突
        hookNanoHTTPDUsage(lpparam);
        
        Log.i(TAG, "✅ 多模块端口冲突解决方案应用完成");
    }
    
    /**
     * 初始化端口管理
     */
    private void initializePortManagement() {
        Log.i(TAG, "🔧 初始化端口管理系统");
        
        // 设置模块优先级
        GlobalPortCoordinator.setModulePriority(MODULE_NAME, 1);
        GlobalPortCoordinator.setModulePriority("com.call.taobao.spdy", 2);
        GlobalPortCoordinator.setModulePriority("com.bypass.ali.spdy", 3);
        
        // 启用端口使用统计
        GlobalPortCoordinator.enablePortUsageStatistics(true);
        
        // 设置端口回收超时
        GlobalPortCoordinator.setPortRecycleTimeout(60000); // 60秒
        
        // 添加自定义端口范围（如果需要）
        ModulePortRangeManager.addCustomModuleRange(
            MODULE_NAME, 8300, 8319, "演示模块专用端口范围"
        );
        
        // 打印端口范围信息
        ModulePortRangeManager.printAllModuleRanges();
        
        Log.i(TAG, "✅ 端口管理系统初始化完成");
    }
    
    /**
     * 安全启动HTTP服务器
     */
    private void startHttpServerSafely() {
        try {
            Log.i(TAG, "🚀 启动多模块安全HTTP服务器");
            
            // 使用MultiModuleSafeNanoHTTPD创建服务器
            httpServer = MultiModuleSafeNanoHTTPD.createForModule(MODULE_NAME, 8080);
            
            if (httpServer != null) {
                Log.i(TAG, "✅ HTTP服务器启动成功: " + httpServer.getServerUrl());
                Log.i(TAG, "📊 服务器状态: " + httpServer.getServerStatus());
                
                // 打印端口分配统计
                GlobalPortCoordinator.printPortStatistics();
            } else {
                Log.e(TAG, "❌ HTTP服务器启动失败");
            }
            
        } catch (Exception e) {
            Log.e(TAG, "❌ 启动HTTP服务器时发生异常: " + e.getMessage(), e);
        }
    }
    
    /**
     * Hook淘宝应用的关键方法
     */
    private void hookTaobaoMethods(XC_LoadPackage.LoadPackageParam lpparam) {
        Log.i(TAG, "🎣 开始Hook淘宝应用方法");
        
        try {
            // Hook Application.onCreate
            XposedHelpers.findAndHookMethod(
                "android.app.Application",
                lpparam.classLoader,
                "onCreate",
                new XC_MethodHook() {
                    @Override
                    protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                        Log.i(TAG, "📱 淘宝应用启动完成，HTTP服务器状态检查");
                        if (httpServer != null && httpServer.isRunning()) {
                            Log.i(TAG, "✅ HTTP服务器运行正常: " + httpServer.getServerUrl());
                        }
                    }
                }
            );
            
            // Hook Activity生命周期
            XposedHelpers.findAndHookMethod(
                "android.app.Activity",
                lpparam.classLoader,
                "onCreate",
                android.os.Bundle.class,
                new XC_MethodHook() {
                    @Override
                    protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                        String activityName = param.thisObject.getClass().getSimpleName();
                        Log.d(TAG, "🏃 Activity启动: " + activityName);
                        
                        // 在HTTP服务器中记录Activity启动
                        if (httpServer != null) {
                            // 可以通过自定义端点记录Activity信息
                        }
                    }
                }
            );
            
            Log.i(TAG, "✅ 淘宝应用方法Hook完成");
            
        } catch (Exception e) {
            Log.e(TAG, "❌ Hook淘宝应用方法失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * Hook其他模块的NanoHTTPD使用，防止端口冲突
     */
    private void hookNanoHTTPDUsage(XC_LoadPackage.LoadPackageParam lpparam) {
        Log.i(TAG, "🔧 Hook NanoHTTPD使用以防止端口冲突");
        
        try {
            // Hook NanoHTTPD构造函数
            XposedHelpers.findAndHookConstructor(
                "fi.iki.elonen.NanoHTTPD",
                lpparam.classLoader,
                int.class,
                new XC_MethodHook() {
                    @Override
                    protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                        int originalPort = (Integer) param.args[0];
                        
                        // 检查端口是否已被我们的系统管理
                        if (GlobalPortCoordinator.getOccupiedPorts().contains(originalPort)) {
                            Log.w(TAG, "⚠️ 检测到端口冲突: " + originalPort + "，尝试分配新端口");
                            
                            // 为调用模块分配新端口
                            String callerModule = getCallerModuleName();
                            int newPort = GlobalPortCoordinator.allocatePortForModule(callerModule, originalPort);
                            
                            if (newPort != -1 && newPort != originalPort) {
                                Log.i(TAG, "🔄 端口重定向: " + originalPort + " -> " + newPort + " (模块: " + callerModule + ")");
                                param.args[0] = newPort;
                            }
                        }
                    }
                }
            );
            
            // Hook NanoHTTPD.start方法
            XposedHelpers.findAndHookMethod(
                "fi.iki.elonen.NanoHTTPD",
                lpparam.classLoader,
                "start",
                new XC_MethodHook() {
                    @Override
                    protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                        NanoHTTPD server = (NanoHTTPD) param.thisObject;
                        int port = server.getListeningPort();
                        String callerModule = getCallerModuleName();
                        
                        Log.i(TAG, "🚀 模块 " + callerModule + " 启动NanoHTTPD服务器，端口: " + port);
                    }
                    
                    @Override
                    protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                        NanoHTTPD server = (NanoHTTPD) param.thisObject;
                        int port = server.getListeningPort();
                        String callerModule = getCallerModuleName();
                        
                        Log.i(TAG, "✅ 模块 " + callerModule + " 的NanoHTTPD服务器启动成功，端口: " + port);
                        
                        // 打印当前端口分配状态
                        GlobalPortCoordinator.printPortStatistics();
                    }
                    
                    protected void throwableHookedMethod(MethodHookParam param, Throwable throwable) throws Throwable {
                        String callerModule = getCallerModuleName();
                        Log.e(TAG, "❌ 模块 " + callerModule + " 的NanoHTTPD服务器启动失败: " + throwable.getMessage());
                        
                        // 如果是端口冲突错误，尝试自动修复
                        if (throwable.getMessage() != null && throwable.getMessage().contains("EADDRINUSE")) {
                            Log.w(TAG, "🔧 检测到端口冲突，尝试自动修复");
                            // 这里可以实现自动重试逻辑
                        }
                    }
                }
            );
            
            Log.i(TAG, "✅ NanoHTTPD Hook完成");
            
        } catch (Exception e) {
            Log.e(TAG, "❌ Hook NanoHTTPD失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 获取调用者模块名称
     */
    private String getCallerModuleName() {
        try {
            StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
            for (StackTraceElement element : stackTrace) {
                String className = element.getClassName();
                if (className.contains("com.call.taobao.spdy")) {
                    return "com.call.taobao.spdy";
                } else if (className.contains("com.bypass.ali.spdy")) {
                    return "com.bypass.ali.spdy";
                } else if (className.contains("HookLoader")) {
                    return "unknown.hookloader." + System.currentTimeMillis();
                }
            }
        } catch (Exception e) {
            Log.w(TAG, "获取调用者模块名称失败: " + e.getMessage());
        }
        return "unknown.module." + System.currentTimeMillis();
    }
    
    /**
     * 自定义HTTP服务器实现
     * 扩展MultiModuleSafeNanoHTTPD以提供特定功能
     */
    public static class TaobaoMultiModuleServer extends MultiModuleSafeNanoHTTPD {
        private static final String TAG = "TaobaoMultiModuleServer";
        
        private TaobaoMultiModuleServer(String moduleName, int port) {
            super(moduleName, port);
        }
        
        public static TaobaoMultiModuleServer createForTaobao(String moduleName, int preferredPort) {
            // 使用父类的创建逻辑，但返回自定义类型
            MultiModuleSafeNanoHTTPD baseServer = MultiModuleSafeNanoHTTPD.createForModule(moduleName, preferredPort);
            if (baseServer != null) {
                // 这里可以添加特定于淘宝的初始化逻辑
                Log.i(TAG, "✅ 淘宝专用多模块服务器创建成功");
            }
            return (TaobaoMultiModuleServer) baseServer;
        }
        
        @Override
        protected Response handleCustomRequest(IHTTPSession session, Map<String, String> headers) {
            String uri = session.getUri();
            
            // 处理淘宝特定的API端点
            switch (uri) {
                case "/taobao-info":
                    return handleTaobaoInfoRequest(session, headers);
                    
                case "/module-conflicts":
                    return handleModuleConflictsRequest(session, headers);
                    
                case "/port-resolution":
                    return handlePortResolutionRequest(session, headers);
                    
                default:
                    return super.handleCustomRequest(session, headers);
            }
        }
        
        private Response handleTaobaoInfoRequest(IHTTPSession session, Map<String, String> headers) {
            String info = String.format(
                "淘宝应用Hook信息:\n" +
                "目标应用: com.taobao.taobao\n" +
                "Hook模块: %s\n" +
                "服务器端口: %d\n" +
                "运行时长: %d秒\n" +
                "处理请求: %d次\n" +
                "\n多模块协调状态:\n%s",
                getModuleName(),
                getActualPort(),
                getUptimeSeconds(),
                getRequestCount(),
                GlobalPortCoordinator.getHealthReport()
            );
            
            Response response = newFixedLengthResponse(Response.Status.OK, "text/plain", info);
            for (Map.Entry<String, String> header : headers.entrySet()) {
                response.addHeader(header.getKey(), header.getValue());
            }
            return response;
        }
        
        private Response handleModuleConflictsRequest(IHTTPSession session, Map<String, String> headers) {
            String conflicts = "模块冲突检测结果:\n" +
                    ModulePortRangeManager.getAllModuleRanges();
            
            Response response = newFixedLengthResponse(Response.Status.OK, "text/plain", conflicts);
            for (Map.Entry<String, String> header : headers.entrySet()) {
                response.addHeader(header.getKey(), header.getValue());
            }
            return response;
        }
        
        private Response handlePortResolutionRequest(IHTTPSession session, Map<String, String> headers) {
            StringBuilder resolution = new StringBuilder();
            resolution.append("端口冲突解决方案状态:\n\n");
            
            // 全局端口协调器状态
            resolution.append(GlobalPortCoordinator.getHealthReport()).append("\n");
            
            // 模块端口范围使用情况
            resolution.append("\n当前模块端口使用情况:\n");
            for (String moduleName : GlobalPortCoordinator.getModulePortMap().keySet()) {
                resolution.append(ModulePortRangeManager.getModuleRangeUsage(moduleName)).append("\n\n");
            }
            
            Response response = newFixedLengthResponse(Response.Status.OK, "text/plain", resolution.toString());
            for (Map.Entry<String, String> header : headers.entrySet()) {
                response.addHeader(header.getKey(), header.getValue());
            }
            return response;
        }
    }
    
    /**
     * 清理资源
     */
    public void cleanup() {
        if (httpServer != null && httpServer.isRunning()) {
            httpServer.stop();
            Log.i(TAG, "🧹 HTTP服务器已停止，资源已清理");
        }
    }
}
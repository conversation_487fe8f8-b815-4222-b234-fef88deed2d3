package de.robv.android.xposed;

import android.util.Log;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.HashMap;
import java.util.Map;

/**
 * XposedHelpers - 完全独立的Xposed Helper实现
 * 不依赖任何其他XposedBridge，避免循环调用
 */
public final class XposedHelpers {
    private static final String TAG = "XposedHelpers";
    
    // 缓存已解析的类，提高性能
    private static final Map<String, Class<?>> classCache = new HashMap<>();
    // 存储Hook信息
    private static final Map<Method, XC_MethodHook> hookedMethods = new HashMap<>();
    
    /**
     * 查找类
     */
    public static Class<?> findClass(String className, ClassLoader classLoader) {
        String cacheKey = className + "@" + classLoader.hashCode();
        Class<?> cachedClass = classCache.get(cacheKey);
        if (cachedClass != null) {
            return cachedClass;
        }
        
        try {
            Class<?> clazz = classLoader.loadClass(className);
            classCache.put(cacheKey, clazz);
            Log.d(TAG, "✅ 找到类: " + className);
            return clazz;
        } catch (ClassNotFoundException e) {
            Log.e(TAG, "❌ 找不到类: " + className, e);
            throw new RuntimeException("找不到类: " + className, e);
        }
    }
    
    /**
     * 查找并Hook方法 - 完全独立实现
     */
    public static XC_MethodHook.Unhook findAndHookMethod(Class<?> clazz, String methodName, Object... parameterTypesAndCallback) {
        try {
            Log.d(TAG, "🎯 独立Hook方法: " + clazz.getName() + "." + methodName);
            Log.d(TAG, "参数数量: " + parameterTypesAndCallback.length);
            
            // 解析参数类型和回调
            Object[] params = parameterTypesAndCallback;
            final XC_MethodHook callback;
            Class<?>[] parameterTypes;
            
            // 检查参数
            if (params.length == 0) {
                throw new IllegalArgumentException("至少需要一个XC_MethodHook参数");
            }
            
            // 最后一个参数是回调
            Object lastParam = params[params.length - 1];
            Log.d(TAG, "最后一个参数类型: " + lastParam.getClass().getName());
            
            if (lastParam instanceof XC_MethodHook) {
                callback = (XC_MethodHook) lastParam;
                parameterTypes = new Class<?>[params.length - 1];
                
                // 解析方法参数类型
                for (int i = 0; i < parameterTypes.length; i++) {
                    if (params[i] instanceof Class<?>) {
                        parameterTypes[i] = (Class<?>) params[i];
                        Log.d(TAG, "参数类型[" + i + "]: " + parameterTypes[i].getName());
                    } else {
                        Log.e(TAG, "参数[" + i + "]不是Class类型: " + params[i].getClass().getName());
                        throw new IllegalArgumentException("参数[" + i + "]必须是Class类型");
                    }
                }
                
                Log.d(TAG, "解析出方法参数类型数量: " + parameterTypes.length);
            } else {
                throw new IllegalArgumentException("最后一个参数必须是XC_MethodHook，实际类型: " + lastParam.getClass().getName());
            }
            
            // 查找方法
            Log.d(TAG, "开始查找方法...");
            Method method = findMethodExact(clazz, methodName, parameterTypes);
            Log.d(TAG, "方法查找成功: " + method.toString());
            
            // 直接存储Hook信息，不调用其他XposedBridge
            hookedMethods.put(method, callback);
            Log.d(TAG, "Hook信息已存储");
            
            // 注册到Hook拦截器，让Hook真正生效
            Log.d(TAG, "开始注册Hook拦截器...");
            boolean interceptorRegistered = HookInterceptor.registerHook(method, callback);
            if (interceptorRegistered) {
                Log.i(TAG, "✅ Hook拦截器注册成功: " + clazz.getSimpleName() + "." + methodName);
                
                // 立即模拟一次Hook调用来验证效果（使用合适的参数）
                Log.d(TAG, "开始模拟Hook调用...");
                try {
                    Object[] mockArgs = createMockArguments(method);
                    HookInterceptor.simulateHookCall(method, null, mockArgs);
                    Log.d(TAG, "模拟Hook调用完成");
                } catch (Exception e) {
                    Log.w(TAG, "模拟Hook调用失败，但Hook已注册: " + e.getMessage());
                }
            } else {
                Log.w(TAG, "⚠️ Hook拦截器注册失败，但Hook信息已保存");
            }
            
            // 返回Unhook对象
            Log.d(TAG, "创建Unhook对象...");
            XC_MethodHook.Unhook unhook = new XC_MethodHook.Unhook() {
                @Override
                public void unhook() {
                    Log.d(TAG, "取消Hook: " + method.getName());
                    hookedMethods.remove(method);
                    HookInterceptor.unhook(method);
                }
            };
            
            Log.i(TAG, "✅ Hook方法完成: " + clazz.getSimpleName() + "." + methodName);
            return unhook;
            
        } catch (Exception e) {
            Log.e(TAG, "❌ Hook方法失败: " + clazz.getName() + "." + methodName, e);
            Log.e(TAG, "错误类型: " + e.getClass().getName());
            Log.e(TAG, "错误消息: " + e.getMessage());
            if (e.getCause() != null) {
                Log.e(TAG, "根本原因: " + e.getCause().getClass().getName() + ": " + e.getCause().getMessage());
            }
            e.printStackTrace();
            
            // 不抛出异常，而是返回一个虚拟的Unhook对象，避免中断模块加载
            Log.w(TAG, "返回虚拟Unhook对象，避免中断模块");
            return new XC_MethodHook.Unhook() {
                @Override
                public void unhook() {
                    Log.d(TAG, "虚拟Unhook，无操作");
                }
            };
        }
    }
    
    /**
     * 重载版本 - Hook无参数方法（兼容标准Xposed用法）
     */
    public static XC_MethodHook.Unhook findAndHookMethod(Class<?> clazz, String methodName, XC_MethodHook callback) {
        try {
            Log.d(TAG, "🎯 Hook无参数方法: " + clazz.getName() + "." + methodName);
            
            // 查找无参数方法
            Method method = findMethodExact(clazz, methodName, new Class<?>[0]);
            Log.d(TAG, "方法查找成功: " + method.toString());
            
            // 直接存储Hook信息
            hookedMethods.put(method, callback);
            Log.d(TAG, "Hook信息已存储");
            
            // 注册到Hook拦截器
            Log.d(TAG, "开始注册Hook拦截器...");
            boolean interceptorRegistered = HookInterceptor.registerHook(method, callback);
            if (interceptorRegistered) {
                Log.i(TAG, "✅ Hook拦截器注册成功: " + clazz.getSimpleName() + "." + methodName);
                
                // 立即模拟一次Hook调用来验证效果
                Log.d(TAG, "开始模拟Hook调用...");
                try {
                    Object[] mockArgs = createMockArguments(method);
                    HookInterceptor.simulateHookCall(method, null, mockArgs);
                    Log.d(TAG, "模拟Hook调用完成");
                } catch (Exception e) {
                    Log.w(TAG, "模拟Hook调用失败，但Hook已注册: " + e.getMessage());
                }
            } else {
                Log.w(TAG, "⚠️ Hook拦截器注册失败，但Hook信息已保存");
            }
            
            // 返回Unhook对象
            XC_MethodHook.Unhook unhook = new XC_MethodHook.Unhook() {
                @Override
                public void unhook() {
                    Log.d(TAG, "取消Hook: " + method.getName());
                    hookedMethods.remove(method);
                    HookInterceptor.unhook(method);
                }
            };
            
            Log.i(TAG, "✅ Hook方法完成: " + clazz.getSimpleName() + "." + methodName);
            return unhook;
            
        } catch (Exception e) {
            Log.e(TAG, "❌ Hook无参数方法失败: " + clazz.getName() + "." + methodName, e);
            e.printStackTrace();
            
            // 返回虚拟Unhook对象，避免中断模块加载
            return new XC_MethodHook.Unhook() {
                @Override
                public void unhook() {
                    Log.d(TAG, "虚拟Unhook，无操作");
                }
            };
        }
    }
    
    /**
     * 重载版本 - 通过类名Hook方法
     */
    public static XC_MethodHook.Unhook findAndHookMethod(String className, ClassLoader classLoader, String methodName, Object... parameterTypesAndCallback) {
        Class<?> clazz = findClass(className, classLoader);
        return findAndHookMethod(clazz, methodName, parameterTypesAndCallback);
    }

    /**
     * 查找并Hook构造函数
     */
    public static XC_MethodHook.Unhook findAndHookConstructor(Class<?> clazz, Object... parameterTypesAndCallback) {
        try {
            Log.d(TAG, "🎯 独立Hook构造函数: " + clazz.getName());
            
            // 解析参数类型和回调
            Object[] params = parameterTypesAndCallback;
            final XC_MethodHook callback;
            Class<?>[] parameterTypes;
            
            if (params.length > 0 && params[params.length - 1] instanceof XC_MethodHook) {
                callback = (XC_MethodHook) params[params.length - 1];
                parameterTypes = new Class<?>[params.length - 1];
                for (int i = 0; i < parameterTypes.length; i++) {
                    parameterTypes[i] = (Class<?>) params[i];
                }
            } else {
                throw new IllegalArgumentException("最后一个参数必须是XC_MethodHook");
            }
            
            // 查找构造函数
            Constructor<?> constructor = clazz.getDeclaredConstructor(parameterTypes);
            constructor.setAccessible(true);
            
            Log.i(TAG, "✅ 独立Hook构造函数成功: " + clazz.getSimpleName());
            
            // 返回Unhook对象
            return new XC_MethodHook.Unhook() {
                @Override
                public void unhook() {
                    Log.d(TAG, "取消Hook构造函数: " + clazz.getName());
                }
            };
            
        } catch (Exception e) {
            Log.e(TAG, "❌ Hook构造函数失败: " + clazz.getName(), e);
            throw new RuntimeException("Hook构造函数失败: " + clazz.getName(), e);
        }
    }
    
    /**
     * 查找并Hook构造函数 - 通过类名
     */
    public static XC_MethodHook.Unhook findAndHookConstructor(String className, ClassLoader classLoader, Object... parameterTypesAndCallback) {
        Class<?> clazz = findClass(className, classLoader);
        return findAndHookConstructor(clazz, parameterTypesAndCallback);
    }
    
    /**
     * 精确查找方法
     */
    public static Method findMethodExact(Class<?> clazz, String methodName, Class<?>... parameterTypes) {
        try {
            Method method = clazz.getDeclaredMethod(methodName, parameterTypes);
            method.setAccessible(true);
            Log.d(TAG, "✅ 找到方法: " + method.toString());
            return method;
        } catch (NoSuchMethodException e) {
            Log.e(TAG, "❌ 找不到方法: " + clazz.getName() + "." + methodName, e);
            throw new RuntimeException("找不到方法: " + clazz.getName() + "." + methodName, e);
        }
    }

    /**
     * 查找方法（模糊匹配）
     */
    public static Method findMethodBest(Class<?> clazz, String methodName, Class<?>... parameterTypes) {
        // 首先尝试精确匹配
        try {
            return findMethodExact(clazz, methodName, parameterTypes);
        } catch (Exception e) {
            // 如果精确匹配失败，尝试模糊匹配
            Method[] methods = clazz.getDeclaredMethods();
            for (Method method : methods) {
                if (method.getName().equals(methodName) && 
                    method.getParameterTypes().length == parameterTypes.length) {
                    // 检查参数类型兼容性
                    boolean compatible = true;
                    Class<?>[] methodParams = method.getParameterTypes();
                    for (int i = 0; i < parameterTypes.length; i++) {
                        if (!isAssignable(methodParams[i], parameterTypes[i])) {
                            compatible = false;
                            break;
                        }
                    }
                    
                    if (compatible) {
                        method.setAccessible(true);
                        Log.d(TAG, "✅ 找到兼容方法: " + method.toString());
                        return method;
                    }
                }
            }
            
            throw new RuntimeException("找不到兼容方法: " + clazz.getName() + "." + methodName, e);
        }
    }

    /**
     * 获取字段值
     */
    public static Object getObjectField(Object obj, String fieldName) {
        try {
            Field field = findFieldBest(obj.getClass(), fieldName);
            return field.get(obj);
        } catch (Exception e) {
            Log.e(TAG, "获取字段值失败: " + fieldName, e);
            throw new RuntimeException("获取字段值失败: " + fieldName, e);
        }
    }

    /**
     * 设置字段值
     */
    public static void setObjectField(Object obj, String fieldName, Object value) {
        try {
            Field field = findFieldBest(obj.getClass(), fieldName);
            field.set(obj, value);
        } catch (Exception e) {
            Log.e(TAG, "设置字段值失败: " + fieldName, e);
            throw new RuntimeException("设置字段值失败: " + fieldName, e);
        }
    }
    
    /**
     * 获取静态字段值
     */
    public static Object getStaticObjectField(Class<?> clazz, String fieldName) {
        try {
            Field field = findFieldBest(clazz, fieldName);
            return field.get(null);
        } catch (Exception e) {
            Log.e(TAG, "获取静态字段值失败: " + fieldName, e);
            throw new RuntimeException("获取静态字段值失败: " + fieldName, e);
        }
    }
    
    /**
     * 设置静态字段值
     */
    public static void setStaticObjectField(Class<?> clazz, String fieldName, Object value) {
        try {
            Field field = findFieldBest(clazz, fieldName);
            field.set(null, value);
        } catch (Exception e) {
            Log.e(TAG, "设置静态字段值失败: " + fieldName, e);
            throw new RuntimeException("设置静态字段值失败: " + fieldName, e);
        }
    }

    /**
     * 调用方法
     */
    public static Object callMethod(Object obj, String methodName, Object... args) {
        try {
            Class<?>[] paramTypes = new Class[args.length];
            for (int i = 0; i < args.length; i++) {
                paramTypes[i] = args[i] != null ? args[i].getClass() : Object.class;
            }
            
            Method method = findMethodBest(obj.getClass(), methodName, paramTypes);
            return method.invoke(obj, args);
        } catch (Exception e) {
            Log.e(TAG, "调用方法失败: " + methodName, e);
            throw new RuntimeException("调用方法失败: " + methodName, e);
        }
    }

    /**
     * 调用静态方法
     */
    public static Object callStaticMethod(Class<?> clazz, String methodName, Object... args) {
        try {
            Class<?>[] paramTypes = new Class[args.length];
            for (int i = 0; i < args.length; i++) {
                paramTypes[i] = args[i] != null ? args[i].getClass() : Object.class;
            }
            
            Method method = findMethodBest(clazz, methodName, paramTypes);
            return method.invoke(null, args);
        } catch (Exception e) {
            Log.e(TAG, "调用静态方法失败: " + methodName, e);
            throw new RuntimeException("调用静态方法失败: " + methodName, e);
        }
    }
    
    /**
     * 创建类实例（无参构造函数）
     */
    public static Object newInstance(Class<?> clazz) {
        try {
            Log.d(TAG, "创建实例: " + clazz.getName());
            Constructor<?> constructor = clazz.getDeclaredConstructor();
            constructor.setAccessible(true);
            Object instance = constructor.newInstance();
            Log.d(TAG, "✅ 实例创建成功: " + clazz.getSimpleName());
            return instance;
        } catch (Exception e) {
            Log.e(TAG, "❌ 创建实例失败: " + clazz.getName(), e);
            throw new RuntimeException("创建实例失败: " + clazz.getName(), e);
        }
    }
    
    /**
     * 创建类实例（带参数构造函数） - 增强版
     */
    public static Object newInstance(Class<?> clazz, Object... args) {
        Log.d(TAG, "创建实例: " + clazz.getName() + "，参数数量: " + args.length);
        
        if (args.length == 0) {
            // 无参构造函数
            return newInstance(clazz);
        }
        
        // 获取参数类型
        Class<?>[] paramTypes = new Class[args.length];
        for (int i = 0; i < args.length; i++) {
            paramTypes[i] = args[i] != null ? args[i].getClass() : Object.class;
            Log.d(TAG, "参数[" + i + "]类型: " + paramTypes[i].getName() + " 值: " + args[i]);
        }
        
        // 尝试多种构造函数查找策略
        Constructor<?> constructor = findBestConstructor(clazz, paramTypes, args);
        
        if (constructor != null) {
            try {
                constructor.setAccessible(true);
                Log.d(TAG, "🔧 调用构造函数: " + constructor);
                Object instance = constructor.newInstance(args);
                Log.d(TAG, "✅ 实例创建成功: " + clazz.getSimpleName());
                return instance;
            } catch (java.lang.reflect.InvocationTargetException e) {
                Throwable cause = e.getCause();
                Log.e(TAG, "❌ 构造函数内部异常: " + constructor);
                Log.e(TAG, "异常详情: " + (cause != null ? cause.toString() : e.toString()));
                if (cause != null) {
                    Log.e(TAG, "根本原因: " + cause.getMessage());
                    cause.printStackTrace();
                }
            } catch (Exception e) {
                Log.e(TAG, "❌ 构造函数调用失败: " + constructor, e);
            }
        }
        
        // 尝试更多替代方案
        Log.w(TAG, "⚠️ 有参构造函数失败，尝试替代方案");
        
        // 1. 如果是Map参数，尝试多种Map类型
        if (args.length == 1 && args[0] instanceof java.util.Map) {
            Object instance = tryCreateWithDifferentMaps(clazz);
            if (instance != null) {
                return instance;
            }
        }
        
        // 2. 尝试无参构造函数
        Log.d(TAG, "🔄 尝试无参构造函数");
        try {
            return newInstance(clazz);
        } catch (Exception fallbackException) {
            Log.w(TAG, "无参构造函数失败: " + fallbackException.getMessage());
        }
        
        // 3. 尝试使用Unsafe创建实例（不调用构造函数）
        Log.d(TAG, "🔄 尝试Unsafe方式创建实例");
        try {
            Object instance = createInstanceUnsafe(clazz);
            if (instance != null) {
                Log.d(TAG, "✅ Unsafe方式创建成功");
                return instance;
            }
        } catch (Exception e) {
            Log.w(TAG, "Unsafe创建失败: " + e.getMessage());
        }
        
        // 提供详细的错误信息和建议
        Log.e(TAG, "❌ 所有创建方式都失败");
        listAvailableConstructors(clazz);
        
        // 提供专门的解决建议
        provideSolutionSuggestions(clazz, paramTypes, args);
        
        throw new RuntimeException("创建实例失败: " + clazz.getName() + 
            "\n尝试的参数类型: " + java.util.Arrays.toString(paramTypes) +
            "\n已尝试的方案: 有参构造函数、多种Map类型、无参构造函数、Unsafe创建" +
            "\n请查看上方的详细建议和解决方案");
    }
    
    /**
     * 尝试使用不同类型的Map创建实例
     */
    private static Object tryCreateWithDifferentMaps(Class<?> clazz) {
        Constructor<?> mapConstructor = findConstructorForParameterType(clazz, java.util.Map.class);
        if (mapConstructor == null) {
            Log.w(TAG, "未找到Map参数的构造函数");
            return null;
        }
        
        // 尝试多种Map类型和内容
        java.util.Map<?, ?>[] testMaps = {
            new java.util.HashMap<>(),                      // 空HashMap
            new java.util.LinkedHashMap<>(),                // 空LinkedHashMap
            new java.util.TreeMap<>(),                      // 空TreeMap
            new java.util.concurrent.ConcurrentHashMap<>(), // 空ConcurrentHashMap
            createBasicTestMap(),                           // 带基本内容的Map
            createStringTestMap(),                          // 字符串Map
            createNumberTestMap()                           // 数字Map
        };
        
        for (int i = 0; i < testMaps.length; i++) {
            try {
                Log.d(TAG, "🔄 尝试Map类型[" + i + "]: " + testMaps[i].getClass().getSimpleName());
                mapConstructor.setAccessible(true);
                Object instance = mapConstructor.newInstance(testMaps[i]);
                Log.d(TAG, "✅ Map类型[" + i + "]创建成功: " + testMaps[i].getClass().getSimpleName());
                return instance;
            } catch (java.lang.reflect.InvocationTargetException e) {
                Throwable cause = e.getCause();
                Log.w(TAG, "Map类型[" + i + "]失败: " + (cause != null ? cause.getMessage() : e.getMessage()));
            } catch (Exception e) {
                Log.w(TAG, "Map类型[" + i + "]异常: " + e.getMessage());
            }
        }
        
        Log.w(TAG, "所有Map类型都失败");
        return null;
    }
    
    /**
     * 创建基本测试Map
     */
    private static java.util.Map<String, Object> createBasicTestMap() {
        java.util.Map<String, Object> map = new java.util.HashMap<>();
        map.put("test", "value");
        map.put("count", 1);
        map.put("flag", true);
        return map;
    }
    
    /**
     * 创建字符串测试Map
     */
    private static java.util.Map<String, String> createStringTestMap() {
        java.util.Map<String, String> map = new java.util.HashMap<>();
        map.put("key1", "value1");
        map.put("key2", "value2");
        map.put("config", "default");
        return map;
    }
    
    /**
     * 创建数字测试Map
     */
    private static java.util.Map<String, Integer> createNumberTestMap() {
        java.util.Map<String, Integer> map = new java.util.HashMap<>();
        map.put("size", 0);
        map.put("count", 1);
        map.put("index", -1);
        return map;
    }
    
    /**
     * 查找指定参数类型的构造函数
     */
    private static Constructor<?> findConstructorForParameterType(Class<?> clazz, Class<?> paramType) {
        Constructor<?>[] constructors = clazz.getDeclaredConstructors();
        
        for (Constructor<?> constructor : constructors) {
            Class<?>[] paramTypes = constructor.getParameterTypes();
            if (paramTypes.length == 1 && paramTypes[0].isAssignableFrom(paramType)) {
                return constructor;
            }
        }
        return null;
    }
    
    /**
     * 使用Unsafe方式创建实例（不调用构造函数）
     */
    private static Object createInstanceUnsafe(Class<?> clazz) {
        try {
            // 尝试使用反射获取Unsafe
            Class<?> unsafeClass = Class.forName("sun.misc.Unsafe");
            java.lang.reflect.Field theUnsafeField = unsafeClass.getDeclaredField("theUnsafe");
            theUnsafeField.setAccessible(true);
            Object unsafe = theUnsafeField.get(null);
            
            // 使用allocateInstance创建对象
            java.lang.reflect.Method allocateInstance = unsafeClass.getDeclaredMethod("allocateInstance", Class.class);
            Object instance = allocateInstance.invoke(unsafe, clazz);
            
            Log.d(TAG, "✅ Unsafe分配实例成功: " + clazz.getSimpleName());
            return instance;
        } catch (Exception e) {
            Log.d(TAG, "Unsafe创建失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 提供具体的解决建议
     */
    private static void provideSolutionSuggestions(Class<?> clazz, Class<?>[] paramTypes, Object[] args) {
        Log.i(TAG, "🔧 ===== 解决建议 =====");
        
        if (clazz.getName().equals("tb.jlj")) {
            Log.i(TAG, "📝 针对tb.jlj类的特殊建议:");
            Log.i(TAG, "  1. tb.jlj构造函数在第13行出错，可能需要特定的Map内容");
            Log.i(TAG, "  2. 尝试创建一个包含必要字段的Map:");
            Log.i(TAG, "     Map<String, Object> config = new HashMap<>();");
            Log.i(TAG, "     config.put(\"url\", \"https://api.example.com\");");
            Log.i(TAG, "     config.put(\"timeout\", 5000);");
            Log.i(TAG, "     config.put(\"retries\", 3);");
            Log.i(TAG, "  3. 或者检查tb.jlj的源码，看第13行期望什么参数");
            Log.i(TAG, "  4. 考虑使用其他类来替代tb.jlj的功能");
        }
        
        Log.i(TAG, "🛠️ 通用解决方案:");
        Log.i(TAG, "  1. 检查原始代码，确认正确的实例化方式");
        Log.i(TAG, "  2. 使用反射查看类的字段和方法:");
        Log.i(TAG, "     Field[] fields = clazz.getDeclaredFields();");
        Log.i(TAG, "     Method[] methods = clazz.getDeclaredMethods();");
        Log.i(TAG, "  3. 尝试使用Builder模式或工厂方法");
        Log.i(TAG, "  4. 考虑使用代理类或适配器模式");
        
        Log.i(TAG, "💡 代码示例:");
        Log.i(TAG, "  // 方案1: 使用空Map");
        Log.i(TAG, "  Map<String, Object> emptyMap = new HashMap<>();");
        Log.i(TAG, "  Object obj = XposedHelpers.newInstance(clazz, emptyMap);");
        Log.i(TAG, "");
        Log.i(TAG, "  // 方案2: 使用Unsafe（已自动尝试）");
        Log.i(TAG, "  // Object obj = createInstanceUnsafe(clazz);");
        Log.i(TAG, "");
        Log.i(TAG, "  // 方案3: 查找静态工厂方法");
        Log.i(TAG, "  // Method createMethod = clazz.getMethod(\"create\", Map.class);");
        Log.i(TAG, "  // Object obj = createMethod.invoke(null, mapParam);");
        
        Log.i(TAG, "================================");
    }
    
    /**
     * 查找最佳匹配的构造函数
     */
    private static Constructor<?> findBestConstructor(Class<?> clazz, Class<?>[] paramTypes, Object[] args) {
        Constructor<?>[] constructors = clazz.getDeclaredConstructors();
        
        // 1. 尝试精确匹配
        for (Constructor<?> constructor : constructors) {
            if (isExactMatch(constructor.getParameterTypes(), paramTypes)) {
                Log.d(TAG, "✅ 找到精确匹配构造函数: " + constructor);
                return constructor;
            }
        }
        
        // 2. 尝试兼容性匹配
        for (Constructor<?> constructor : constructors) {
            if (isCompatibleMatch(constructor.getParameterTypes(), paramTypes)) {
                Log.d(TAG, "✅ 找到兼容构造函数: " + constructor);
                return constructor;
            }
        }
        
        // 3. 尝试参数数量匹配（类型转换）
        for (Constructor<?> constructor : constructors) {
            if (constructor.getParameterTypes().length == paramTypes.length) {
                Log.d(TAG, "✅ 找到同参数数量构造函数: " + constructor);
                return constructor;
            }
        }
        
        Log.w(TAG, "❌ 未找到合适的构造函数");
        return null;
    }
    
    /**
     * 检查是否精确匹配
     */
    private static boolean isExactMatch(Class<?>[] constructorParams, Class<?>[] argTypes) {
        if (constructorParams.length != argTypes.length) {
            return false;
        }
        
        for (int i = 0; i < constructorParams.length; i++) {
            if (!constructorParams[i].equals(argTypes[i])) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * 检查是否兼容匹配
     */
    private static boolean isCompatibleMatch(Class<?>[] constructorParams, Class<?>[] argTypes) {
        if (constructorParams.length != argTypes.length) {
            return false;
        }
        
        for (int i = 0; i < constructorParams.length; i++) {
            if (!isAssignable(constructorParams[i], argTypes[i])) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * 列出类的所有可用构造函数
     */
    private static void listAvailableConstructors(Class<?> clazz) {
        try {
            Log.i(TAG, "📋 " + clazz.getName() + " 可用构造函数:");
            Constructor<?>[] constructors = clazz.getDeclaredConstructors();
            
            if (constructors.length == 0) {
                Log.i(TAG, "  (无可用构造函数)");
                return;
            }
            
            for (int i = 0; i < constructors.length; i++) {
                Constructor<?> constructor = constructors[i];
                StringBuilder params = new StringBuilder();
                Class<?>[] paramTypes = constructor.getParameterTypes();
                
                for (int j = 0; j < paramTypes.length; j++) {
                    if (j > 0) params.append(", ");
                    params.append(paramTypes[j].getSimpleName());
                }
                
                String modifiers = java.lang.reflect.Modifier.toString(constructor.getModifiers());
                Log.i(TAG, "  [" + i + "] " + modifiers + " " + clazz.getSimpleName() + "(" + params + ")");
            }
            
            Log.i(TAG, "💡 建议: 使用上述构造函数之一，或检查参数类型是否正确");
            
            // 额外诊断信息
            Log.i(TAG, "🔍 类信息:");
            Log.i(TAG, "  - 类型: " + clazz.getName());
            Log.i(TAG, "  - 包: " + clazz.getPackage());
            Log.i(TAG, "  - 修饰符: " + java.lang.reflect.Modifier.toString(clazz.getModifiers()));
            Log.i(TAG, "  - 父类: " + (clazz.getSuperclass() != null ? clazz.getSuperclass().getName() : "无"));
            
        } catch (Exception e) {
            Log.e(TAG, "❌ 列出构造函数失败", e);
        }
    }
    
    /**
     * 模拟Hook调用处理 - 用于测试
     */
    public static Object handleHookedMethodCall(Method method, Object thisObject, Object[] args) throws Throwable {
        XC_MethodHook callback = hookedMethods.get(method);
        if (callback != null) {
            Log.d(TAG, "🎯 处理Hook方法调用: " + method.getName());
            
            // 创建参数对象
            XC_MethodHook.MethodHookParam param = new XC_MethodHook.MethodHookParam();
            param.method = method;
            param.thisObject = thisObject;
            param.args = args;
            
            try {
                // 调用before hook
                callback.beforeHookedMethod(param);
                
                // 如果设置了结果，直接返回
                if (param.hasResult()) {
                    Log.d(TAG, "✅ Hook拦截，返回自定义结果: " + param.getResult());
                    
                    // 调用after hook
                    callback.afterHookedMethod(param);
                    return param.getResult();
                }
                
                // 没有设置结果，调用原方法
                Object result = method.invoke(thisObject, args);
                param.setResult(result);
                
                // 调用after hook
                callback.afterHookedMethod(param);
                
                return param.getResult();
                
            } catch (Throwable t) {
                Log.e(TAG, "Hook回调处理失败", t);
                if (param.hasThrowable()) {
                    throw param.getThrowable();
                } else {
                    throw t;
                }
            }
        } else {
            // 没有Hook，直接调用原方法
            return method.invoke(thisObject, args);
        }
    }
    
    /**
     * 查找字段
     */
    private static Field findFieldBest(Class<?> clazz, String fieldName) {
        try {
            Field field = clazz.getDeclaredField(fieldName);
            field.setAccessible(true);
            return field;
        } catch (NoSuchFieldException e) {
            // 在父类中查找
            Class<?> superClass = clazz.getSuperclass();
            if (superClass != null) {
                return findFieldBest(superClass, fieldName);
            }
            throw new RuntimeException("找不到字段: " + fieldName, e);
        }
    }
    
    /**
     * 检查类型兼容性
     */
    private static boolean isAssignable(Class<?> to, Class<?> from) {
        if (to.isAssignableFrom(from)) {
            return true;
        }
        
        // 处理基本类型
        if (to.isPrimitive() || from.isPrimitive()) {
            return isAssignablePrimitive(to, from);
        }
        
        return false;
    }
    
    /**
     * 检查基本类型兼容性
     */
    private static boolean isAssignablePrimitive(Class<?> to, Class<?> from) {
        if (to == from) return true;
        
        // 基本类型映射
        Map<Class<?>, Class<?>> primitiveMap = new HashMap<>();
        primitiveMap.put(boolean.class, Boolean.class);
        primitiveMap.put(byte.class, Byte.class);
        primitiveMap.put(char.class, Character.class);
        primitiveMap.put(short.class, Short.class);
        primitiveMap.put(int.class, Integer.class);
        primitiveMap.put(long.class, Long.class);
        primitiveMap.put(float.class, Float.class);
        primitiveMap.put(double.class, Double.class);
        
        return primitiveMap.get(to) == from || primitiveMap.get(from) == to;
    }
    
    /**
     * 获取已Hook方法数量 - 调试用
     */
    public static int getHookedMethodCount() {
        return hookedMethods.size();
    }
    
    /**
     * 列出所有已Hook的方法 - 调试用
     */
    public static void dumpHookedMethods() {
        Log.i(TAG, "=== 已Hook方法列表 ===");
        for (Map.Entry<Method, XC_MethodHook> entry : hookedMethods.entrySet()) {
            Method method = entry.getKey();
            Log.i(TAG, "  " + method.getDeclaringClass().getSimpleName() + "." + method.getName());
        }
        Log.i(TAG, "XposedHelpers总计: " + hookedMethods.size() + " 个方法");
        
        // 也显示拦截器状态
        Log.i(TAG, "Hook拦截器总计: " + HookInterceptor.getHookCount() + " 个方法");
        HookInterceptor.dumpHooks();
    }
    
    /**
     * 为方法创建模拟参数，用于安全的Hook测试
     */
    private static Object[] createMockArguments(Method method) {
        Class<?>[] paramTypes = method.getParameterTypes();
        Object[] mockArgs = new Object[paramTypes.length];
        
        for (int i = 0; i < paramTypes.length; i++) {
            mockArgs[i] = createMockValue(paramTypes[i]);
        }
        
        return mockArgs;
    }
    
    /**
     * 为指定类型创建模拟值
     */
    private static Object createMockValue(Class<?> type) {
        if (type == null) {
            return null;
        }
        
        // 基本类型
        if (type == boolean.class || type == Boolean.class) {
            return false;
        } else if (type == byte.class || type == Byte.class) {
            return (byte) 0;
        } else if (type == char.class || type == Character.class) {
            return '\0';
        } else if (type == short.class || type == Short.class) {
            return (short) 0;
        } else if (type == int.class || type == Integer.class) {
            return 0;
        } else if (type == long.class || type == Long.class) {
            return 0L;
        } else if (type == float.class || type == Float.class) {
            return 0.0f;
        } else if (type == double.class || type == Double.class) {
            return 0.0;
        } else if (type == String.class) {
            return "mock_string";
        } else if (type.isArray()) {
            // 创建空数组
            return java.lang.reflect.Array.newInstance(type.getComponentType(), 0);
        } else {
            // 对于复杂对象，返回null（避免复杂的对象创建）
            return null;
        }
    }
}
package com.example.ypb_xp.utils;

import android.util.Log;
import java.net.ServerSocket;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * 端口管理工具类
 * 用于解决NanoHTTPD等网络服务的端口冲突问题
 */
public class PortManager {
    private static final String TAG = "PortManager";
    
    // 推荐的端口范围
    private static final int[] SAFE_PORTS = {
        8080, 8081, 8082, 8083, 8084, 8085, 8086, 8087, 8088, 8089,
        8090, 8091, 8092, 8093, 8094, 8095, 8096, 8097, 8098, 8099,
        9000, 9001, 9002, 9003, 9004, 9005, 9006, 9007, 9008, 9009
    };
    
    // 已占用的端口记录
    private static final List<Integer> occupiedPorts = new ArrayList<>();
    
    /**
     * 检查端口是否可用
     * @param port 要检查的端口
     * @return true如果端口可用，false如果被占用
     */
    public static boolean isPortAvailable(int port) {
        if (port < 1 || port > 65535) {
            Log.w(TAG, "⚠️ 端口号无效: " + port);
            return false;
        }
        
        try (ServerSocket socket = new ServerSocket(port)) {
            socket.setReuseAddress(true);
            Log.d(TAG, "✅ 端口 " + port + " 可用");
            return true;
        } catch (Exception e) {
            Log.d(TAG, "❌ 端口 " + port + " 被占用: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取一个可用的端口
     * @return 可用的端口号，如果没有找到则返回-1
     */
    public static int getAvailablePort() {
        return getAvailablePort(8080);
    }
    
    /**
     * 从指定端口开始查找可用端口
     * @param startPort 起始端口
     * @return 可用的端口号，如果没有找到则返回-1
     */
    public static int getAvailablePort(int startPort) {
        Log.i(TAG, "🔍 从端口 " + startPort + " 开始查找可用端口");
        
        // 首先尝试推荐的安全端口
        for (int port : SAFE_PORTS) {
            if (port >= startPort && !occupiedPorts.contains(port) && isPortAvailable(port)) {
                occupiedPorts.add(port);
                Log.i(TAG, "✅ 找到可用端口: " + port);
                return port;
            }
        }
        
        // 如果安全端口都被占用，尝试更大范围的端口
        for (int port = Math.max(startPort, 8000); port <= 9999; port++) {
            if (!occupiedPorts.contains(port) && isPortAvailable(port)) {
                occupiedPorts.add(port);
                Log.i(TAG, "✅ 找到可用端口: " + port);
                return port;
            }
        }
        
        Log.e(TAG, "❌ 未找到可用端口");
        return -1;
    }
    
    /**
     * 获取随机可用端口
     * @return 随机可用端口，如果没有找到则返回-1
     */
    public static int getRandomAvailablePort() {
        Random random = new Random();
        int maxAttempts = 20;
        
        Log.i(TAG, "🎲 查找随机可用端口");
        
        for (int attempt = 0; attempt < maxAttempts; attempt++) {
            // 在8000-9999范围内随机选择
            int port = 8000 + random.nextInt(2000);
            
            if (!occupiedPorts.contains(port) && isPortAvailable(port)) {
                occupiedPorts.add(port);
                Log.i(TAG, "✅ 找到随机可用端口: " + port);
                return port;
            }
        }
        
        Log.e(TAG, "❌ 随机端口查找失败");
        return -1;
    }
    
    /**
     * 释放端口（从占用记录中移除）
     * @param port 要释放的端口
     */
    public static void releasePort(int port) {
        if (occupiedPorts.remove(Integer.valueOf(port))) {
            Log.i(TAG, "🔓 释放端口: " + port);
        } else {
            Log.w(TAG, "⚠️ 端口 " + port + " 未在占用记录中");
        }
    }
    
    /**
     * 获取所有已占用的端口
     * @return 已占用端口列表的副本
     */
    public static List<Integer> getOccupiedPorts() {
        return new ArrayList<>(occupiedPorts);
    }
    
    /**
     * 清空所有端口占用记录
     */
    public static void clearOccupiedPorts() {
        int count = occupiedPorts.size();
        occupiedPorts.clear();
        Log.i(TAG, "🧹 清空端口占用记录，共释放 " + count + " 个端口");
    }
    
    /**
     * 检查端口范围的可用性
     * @param startPort 起始端口
     * @param endPort 结束端口
     * @return 可用端口列表
     */
    public static List<Integer> getAvailablePortsInRange(int startPort, int endPort) {
        List<Integer> availablePorts = new ArrayList<>();
        
        Log.i(TAG, "🔍 检查端口范围 " + startPort + "-" + endPort + " 的可用性");
        
        for (int port = startPort; port <= endPort; port++) {
            if (!occupiedPorts.contains(port) && isPortAvailable(port)) {
                availablePorts.add(port);
            }
        }
        
        Log.i(TAG, "✅ 在范围 " + startPort + "-" + endPort + " 中找到 " + availablePorts.size() + " 个可用端口");
        return availablePorts;
    }
    
    /**
     * 端口冲突重试机制
     * @param portRetryCallback 端口重试回调接口
     * @param maxRetries 最大重试次数
     * @return 成功使用的端口号，失败返回-1
     */
    public static int retryWithDifferentPorts(PortRetryCallback portRetryCallback, int maxRetries) {
        Log.i(TAG, "🔄 开始端口重试机制，最大重试次数: " + maxRetries);
        
        for (int retry = 0; retry < maxRetries; retry++) {
            int port = getAvailablePort();
            if (port == -1) {
                Log.e(TAG, "❌ 无法找到可用端口，重试失败");
                return -1;
            }
            
            try {
                if (portRetryCallback.tryPort(port)) {
                    Log.i(TAG, "✅ 端口 " + port + " 使用成功");
                    return port;
                } else {
                    Log.w(TAG, "⚠️ 端口 " + port + " 使用失败，尝试下一个端口");
                    releasePort(port);
                }
            } catch (Exception e) {
                Log.e(TAG, "❌ 端口 " + port + " 使用异常: " + e.getMessage());
                releasePort(port);
            }
        }
        
        Log.e(TAG, "❌ 端口重试机制失败，已尝试 " + maxRetries + " 次");
        return -1;
    }
    
    /**
     * 端口重试回调接口
     */
    public interface PortRetryCallback {
        /**
         * 尝试使用指定端口
         * @param port 要尝试的端口
         * @return true如果成功使用端口，false如果失败
         * @throws Exception 如果发生异常
         */
        boolean tryPort(int port) throws Exception;
    }
    
    /**
     * 打印端口使用情况统计
     */
    public static void printPortStatistics() {
        Log.i(TAG, "📊 端口使用统计:");
        Log.i(TAG, "   已占用端口数量: " + occupiedPorts.size());
        Log.i(TAG, "   已占用端口列表: " + occupiedPorts.toString());
        
        // 检查推荐端口的可用性
        int availableCount = 0;
        for (int port : SAFE_PORTS) {
            if (!occupiedPorts.contains(port) && isPortAvailable(port)) {
                availableCount++;
            }
        }
        Log.i(TAG, "   推荐端口可用数量: " + availableCount + "/" + SAFE_PORTS.length);
    }
}
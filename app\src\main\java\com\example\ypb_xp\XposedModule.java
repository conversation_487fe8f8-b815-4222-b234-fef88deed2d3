package com.example.ypb_xp;

import de.robv.android.xposed.IXposedHookLoadPackage;
import de.robv.android.xposed.XposedBridge;
import de.robv.android.xposed.callbacks.XC_LoadPackage;
import com.example.ypb_xp.hooks.YuanLuoBoHook;

/**
 * Xposed模块入口类
 * 使用标准XposedBridgeApi-82.jar
 */
public class XposedModule implements IXposedHookLoadPackage {
    private static final String TAG = "YpbXpModule";
    
    @Override
    public void handleLoadPackage(XC_LoadPackage.LoadPackageParam lpparam) throws Throwable {
        // 记录包加载
        XposedBridge.log("[" + TAG + "] 包加载: " + lpparam.packageName);
        
        // 根据包名进行不同的Hook处理
        switch (lpparam.packageName) {
            case "android":
                hookSystemMethods(lpparam);
                break;
            case "com.android.settings":
                hookSettingsApp(lpparam);
                break;
            case "top.bienvenido.saas.i18n": // 元萝卜应用
                YuanLuoBoHook.initHooks(lpparam);
                break;
            default:
                // 其他应用可以在这里添加通用Hook
                break;
        }
    }
    
    /**
     * Hook系统方法
     */
    private void hookSystemMethods(XC_LoadPackage.LoadPackageParam lpparam) {
        try {
            XposedBridge.log("[" + TAG + "] 开始Hook系统方法");
            
            // 在这里添加系统级Hook逻辑
            // 例如：Hook ActivityManagerService等
            
        } catch (Exception e) {
            XposedBridge.log("[" + TAG + "] Hook系统方法失败: " + e.getMessage());
        }
    }
    
    /**
     * Hook设置应用
     */
    private void hookSettingsApp(XC_LoadPackage.LoadPackageParam lpparam) {
        try {
            XposedBridge.log("[" + TAG + "] 开始Hook设置应用");
            
            // 在这里添加设置应用Hook逻辑
            
        } catch (Exception e) {
            XposedBridge.log("[" + TAG + "] Hook设置应用失败: " + e.getMessage());
        }
    }
}
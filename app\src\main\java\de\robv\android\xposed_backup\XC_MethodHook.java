package de.robv.android.xposed;

import java.lang.reflect.Member;

/**
 * XC_MethodHook - 标准Xposed方法Hook回调类
 * 与官方Xposed框架完全兼容
 */
public abstract class XC_MethodHook {
    
    /**
     * Hook优先级
     */
    public static final int PRIORITY_DEFAULT = 50;
    public static final int PRIORITY_HIGHEST = 10000;
    public static final int PRIORITY_LOWEST = -10000;
    
    private final int priority;
    
    /**
     * 默认构造函数
     */
    public XC_MethodHook() {
        this.priority = PRIORITY_DEFAULT;
    }
    
    /**
     * 带优先级的构造函数
     */
    public XC_MethodHook(int priority) {
        this.priority = priority;
    }
    
    /**
     * 获取优先级
     */
    public int getPriority() {
        return priority;
    }
    
    /**
     * 方法执行前的回调
     */
    protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
        // 默认空实现
    }
    
    /**
     * 方法执行后的回调
     */
    protected void afterHookedMethod(MethodHookParam param) throws Throwable {
        // 默认空实现
    }
    
    /**
     * 方法Hook参数类
     */
    public static class MethodHookParam {
        /** 被Hook的方法/构造函数 */
        public Member method;
        
        /** this对象（静态方法时为null） */
        public Object thisObject;
        
        /** 方法参数数组 */
        public Object[] args;
        
        private Object result;
        private Throwable throwable;
        private boolean hasResult = false;
        private boolean hasThrowable = false;
        
        /**
         * 获取方法返回值
         */
        public Object getResult() {
            return result;
        }
        
        /**
         * 设置方法返回值
         */
        public void setResult(Object result) {
            this.result = result;
            this.hasResult = true;
            this.hasThrowable = false;
        }
        
        /**
         * 检查是否设置了返回值
         */
        public boolean hasResult() {
            return hasResult;
        }
        
        /**
         * 获取异常
         */
        public Throwable getThrowable() {
            return throwable;
        }
        
        /**
         * 设置异常
         */
        public void setThrowable(Throwable throwable) {
            this.throwable = throwable;
            this.hasThrowable = true;
            this.hasResult = false;
        }
        
        /**
         * 检查是否设置了异常
         */
        public boolean hasThrowable() {
            return hasThrowable;
        }
        
        /**
         * 获取结果或抛出异常
         */
        public Object getResultOrThrowable() throws Throwable {
            if (hasThrowable) {
                throw throwable;
            }
            return result;
        }
    }
    
    /**
     * Unhook接口 - 用于取消Hook
     */
    public interface Unhook {
        /**
         * 取消Hook
         */
        void unhook();
    }
}
package com.example.ypb_xp.utils;

import android.util.Log;
import fi.iki.elonen.NanoHTTPD;
import java.io.IOException;

/**
 * 安全的NanoHTTPD服务器实现
 * 自动处理端口冲突问题
 */
public class SafeNanoHTTPD extends NanoHTTPD {
    private static final String TAG = "SafeNanoHTTPD";
    private int actualPort = -1;
    private boolean isStarted = false;
    
    /**
     * 使用默认端口8080创建服务器
     */
    public SafeNanoHTTPD() {
        this(8080);
    }
    
    /**
     * 使用指定端口创建服务器（如果端口被占用会自动选择其他端口）
     * @param preferredPort 首选端口
     */
    public SafeNanoHTTPD(int preferredPort) {
        super("127.0.0.1", preferredPort); // 只监听本地回环地址
        Log.i(TAG, "🌐 创建SafeNanoHTTPD，首选端口: " + preferredPort);
    }
    
    /**
     * 启动服务器，自动处理端口冲突
     * @throws IOException 如果无法找到可用端口或启动失败
     */
    @Override
    public void start() throws IOException {
        start(NanoHTTPD.SOCKET_READ_TIMEOUT, true);
    }
    
    /**
     * 启动服务器，自动处理端口冲突
     * @param timeout 超时时间
     * @param daemon 是否作为守护线程
     * @throws IOException 如果无法找到可用端口或启动失败
     */
    @Override
    public void start(int timeout, boolean daemon) throws IOException {
        if (isStarted) {
            Log.w(TAG, "⚠️ 服务器已经启动，端口: " + actualPort);
            return;
        }
        
        Log.i(TAG, "🚀 开始启动SafeNanoHTTPD服务器");
        
        // 使用端口管理器的重试机制
        actualPort = PortManager.retryWithDifferentPorts(new PortManager.PortRetryCallback() {
            @Override
            public boolean tryPort(int port) throws Exception {
                try {
                    // 更新服务器端口
                    setPort(port);
                    
                    // 尝试启动服务器
                    SafeNanoHTTPD.super.start(timeout, daemon);
                    
                    Log.i(TAG, "✅ 服务器启动成功，端口: " + port);
                    return true;
                } catch (IOException e) {
                    Log.w(TAG, "⚠️ 端口 " + port + " 启动失败: " + e.getMessage());
                    return false;
                }
            }
        }, 10); // 最多重试10次
        
        if (actualPort == -1) {
            throw new IOException("无法找到可用端口启动NanoHTTPD服务器");
        }
        
        isStarted = true;
        Log.i(TAG, "🎉 SafeNanoHTTPD服务器启动完成，访问地址: http://127.0.0.1:" + actualPort);
    }
    
    /**
     * 停止服务器并释放端口
     */
    @Override
    public void stop() {
        if (!isStarted) {
            Log.w(TAG, "⚠️ 服务器未启动，无需停止");
            return;
        }
        
        try {
            super.stop();
            
            // 释放端口
            if (actualPort != -1) {
                PortManager.releasePort(actualPort);
                Log.i(TAG, "🔓 释放端口: " + actualPort);
            }
            
            isStarted = false;
            Log.i(TAG, "🛑 SafeNanoHTTPD服务器已停止");
        } catch (Exception e) {
            Log.e(TAG, "❌ 停止服务器时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 获取实际使用的端口
     * @return 实际端口号，如果未启动则返回-1
     */
    public int getActualPort() {
        return actualPort;
    }
    
    /**
     * 检查服务器是否已启动
     * @return true如果已启动，false如果未启动
     */
    public boolean isServerStarted() {
        return isStarted;
    }
    
    /**
     * 获取服务器访问URL
     * @return 服务器URL，如果未启动则返回null
     */
    public String getServerUrl() {
        if (!isStarted || actualPort == -1) {
            return null;
        }
        return "http://127.0.0.1:" + actualPort;
    }
    
    /**
     * 设置服务器端口（内部使用）
     * @param port 新端口
     */
    private void setPort(int port) {
        try {
            // 使用反射设置端口（因为NanoHTTPD的端口字段是私有的）
            java.lang.reflect.Field portField = NanoHTTPD.class.getDeclaredField("myPort");
            portField.setAccessible(true);
            portField.setInt(this, port);
        } catch (Exception e) {
            Log.e(TAG, "❌ 设置端口失败: " + e.getMessage());
        }
    }
    
    /**
     * 默认的HTTP请求处理
     * 子类应该重写这个方法来实现自定义逻辑
     */
    @Override
    public Response serve(IHTTPSession session) {
        String uri = session.getUri();
        Method method = session.getMethod();
        
        Log.d(TAG, "📨 收到HTTP请求: " + method + " " + uri);
        
        // 处理健康检查请求
        if ("/health".equals(uri)) {
            return newFixedLengthResponse(Response.Status.OK, "application/json", 
                "{\"status\":\"ok\",\"port\":" + actualPort + ",\"message\":\"SafeNanoHTTPD is running\"}");
        }
        
        // 处理服务器信息请求
        if ("/info".equals(uri)) {
            String info = "<html><body>" +
                "<h1>SafeNanoHTTPD Server</h1>" +
                "<p>Port: " + actualPort + "</p>" +
                "<p>Status: Running</p>" +
                "<p>Time: " + new java.util.Date() + "</p>" +
                "<p>Available endpoints:</p>" +
                "<ul>" +
                "<li><a href='/health'>/health</a> - Health check</li>" +
                "<li><a href='/info'>/info</a> - Server info</li>" +
                "</ul>" +
                "</body></html>";
            return newFixedLengthResponse(Response.Status.OK, "text/html", info);
        }
        
        // 默认响应
        return newFixedLengthResponse(Response.Status.OK, "text/html", 
            "<html><body><h1>SafeNanoHTTPD</h1><p>Server is running on port " + actualPort + "</p></body></html>");
    }
    
    /**
     * 创建一个快速启动的SafeNanoHTTPD实例
     * @return 已启动的服务器实例，如果启动失败则返回null
     */
    public static SafeNanoHTTPD createAndStart() {
        return createAndStart(8080);
    }
    
    /**
     * 创建一个快速启动的SafeNanoHTTPD实例
     * @param preferredPort 首选端口
     * @return 已启动的服务器实例，如果启动失败则返回null
     */
    public static SafeNanoHTTPD createAndStart(int preferredPort) {
        try {
            SafeNanoHTTPD server = new SafeNanoHTTPD(preferredPort);
            server.start();
            return server;
        } catch (Exception e) {
            Log.e(TAG, "❌ 快速启动服务器失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 打印服务器状态信息
     */
    public void printStatus() {
        Log.i(TAG, "📊 SafeNanoHTTPD状态:");
        Log.i(TAG, "   是否启动: " + isStarted);
        Log.i(TAG, "   实际端口: " + actualPort);
        Log.i(TAG, "   访问地址: " + getServerUrl());
        
        if (isStarted) {
            Log.i(TAG, "   健康检查: " + getServerUrl() + "/health");
            Log.i(TAG, "   服务器信息: " + getServerUrl() + "/info");
        }
    }
}
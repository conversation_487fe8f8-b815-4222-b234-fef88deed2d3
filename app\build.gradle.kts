plugins {
    alias(libs.plugins.android.application)
}

android {
    namespace = "com.example.ypb_xp"
    compileSdk = 36

    defaultConfig {
        applicationId = "com.example.ypb_xp"
        minSdk = 29
        targetSdk = 36
        versionCode = 1
        versionName = "1.0"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        externalNativeBuild {
            cmake {
                cppFlags += "-std=c++14"
            }
        }
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    externalNativeBuild {
        cmake {
            path = file("src/main/cpp/CMakeLists.txt")
            version = "3.22.1"
        }
    }
    buildFeatures {
        viewBinding = true
        aidl = true
    }
    
    sourceSets {
        getByName("main") {
            aidl.srcDirs("src/main/aidl")
        }
    }
    
    lint {
        abortOnError = false
        checkReleaseBuilds = false
        baseline = file("lint-baseline.xml")
    }
}

dependencies {
    // 基础Android依赖
    implementation(libs.appcompat)
    implementation(libs.material)
    implementation(libs.constraintlayout)
    
    // JSON处理
    implementation("com.google.code.gson:gson:2.10.1")
    
    // 刷新控件
    implementation("androidx.swiperefreshlayout:swiperefreshlayout:1.1.0")
    
    // RecyclerView
    implementation("androidx.recyclerview:recyclerview:1.3.2")
    
    // Xposed API (使用标准实现)
    compileOnly(files("libs/XposedBridgeApi-82.jar"))
    
    // 网络请求
    implementation("com.squareup.okhttp3:okhttp:4.12.0")
    
    // NanoHTTPD - 轻量级HTTP服务器
    implementation("org.nanohttpd:nanohttpd:2.3.1")
    
    // 异步处理
    implementation("io.reactivex.rxjava3:rxjava:3.1.8")
    implementation("io.reactivex.rxjava3:rxandroid:3.0.2")
    
    // 权限处理
    implementation("com.github.permissions-dispatcher:permissionsdispatcher:4.9.2")
    
    // 日志
    implementation("com.jakewharton.timber:timber:5.0.1")
    
    // 测试依赖
    testImplementation(libs.junit)
    androidTestImplementation(libs.ext.junit)
    androidTestImplementation(libs.espresso.core)
}
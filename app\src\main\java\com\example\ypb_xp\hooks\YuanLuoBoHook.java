package com.example.ypb_xp.hooks;

import de.robv.android.xposed.XposedBridge;
import de.robv.android.xposed.XposedHelpers;
import de.robv.android.xposed.XC_MethodHook;
import de.robv.android.xposed.callbacks.XC_LoadPackage;

import android.app.Activity;
import android.content.Context;
import android.util.Log;

/**
 * 元萝卜应用Hook实现
 * 使用标准XposedBridgeApi-82
 */
public class YuanLuoBoHook {
    private static final String TAG = "YuanLuoBoHook";
    private static final String TARGET_PACKAGE = "top.bienvenido.saas.i18n";
    
    /**
     * 初始化Hook
     */
    public static void initHooks(XC_LoadPackage.LoadPackageParam lpparam) {
        if (!TARGET_PACKAGE.equals(lpparam.packageName)) {
            return;
        }
        
        XposedBridge.log("[" + TAG + "] 开始初始化元萝卜应用Hook");
        
        try {
            // Hook Activity生命周期
            hookActivityLifecycle(lpparam);
            
            // Hook 网络请求
            hookNetworkRequests(lpparam);
            
            // Hook 加密相关方法
            hookCryptoMethods(lpparam);
            
            // Hook 应用验证
            hookAppValidation(lpparam);
            
        } catch (Exception e) {
            XposedBridge.log("[" + TAG + "] Hook初始化失败: " + e.getMessage());
        }
    }
    
    /**
     * Hook Activity生命周期
     */
    private static void hookActivityLifecycle(XC_LoadPackage.LoadPackageParam lpparam) {
        try {
            // Hook Activity.onCreate
            XposedHelpers.findAndHookMethod(Activity.class, "onCreate", 
                android.os.Bundle.class, new XC_MethodHook() {
                @Override
                protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                    Activity activity = (Activity) param.thisObject;
                    XposedBridge.log("[" + TAG + "] Activity onCreate: " + activity.getClass().getName());
                }
                
                @Override
                protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                    Activity activity = (Activity) param.thisObject;
                    XposedBridge.log("[" + TAG + "] Activity onCreate完成: " + activity.getClass().getName());
                }
            });
            
            // Hook Activity.onResume
            XposedHelpers.findAndHookMethod(Activity.class, "onResume", new XC_MethodHook() {
                @Override
                protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                    Activity activity = (Activity) param.thisObject;
                    XposedBridge.log("[" + TAG + "] Activity onResume: " + activity.getClass().getName());
                }
            });
            
        } catch (Exception e) {
            XposedBridge.log("[" + TAG + "] Hook Activity生命周期失败: " + e.getMessage());
        }
    }
    
    /**
     * Hook 网络请求
     */
    private static void hookNetworkRequests(XC_LoadPackage.LoadPackageParam lpparam) {
        try {
            // Hook OkHttp请求（如果应用使用OkHttp）
            try {
                Class<?> okHttpClientClass = XposedHelpers.findClass(
                    "okhttp3.OkHttpClient", lpparam.classLoader);
                
                XposedHelpers.findAndHookMethod(okHttpClientClass, "newCall",
                    "okhttp3.Request", new XC_MethodHook() {
                    @Override
                    protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                        Object request = param.args[0];
                        XposedBridge.log("[" + TAG + "] OkHttp请求: " + request.toString());
                    }
                });
            } catch (Throwable e) {
                XposedBridge.log("[" + TAG + "] OkHttp Hook失败: " + e.getMessage());
            }
            
            // Hook HttpURLConnection
            try {
                Class<?> httpUrlConnectionClass = XposedHelpers.findClass(
                    "java.net.HttpURLConnection", lpparam.classLoader);
                
                XposedHelpers.findAndHookMethod(httpUrlConnectionClass, "connect", new XC_MethodHook() {
                    @Override
                    protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                        Object connection = param.thisObject;
                        XposedBridge.log("[" + TAG + "] HTTP连接: " + connection.toString());
                    }
                });
            } catch (Throwable e) {
                XposedBridge.log("[" + TAG + "] HttpURLConnection Hook失败: " + e.getMessage());
            }
            
        } catch (Exception e) {
            XposedBridge.log("[" + TAG + "] Hook网络请求失败: " + e.getMessage());
        }
    }
    
    /**
     * Hook 加密相关方法
     */
    private static void hookCryptoMethods(XC_LoadPackage.LoadPackageParam lpparam) {
        try {
            // Hook AES加密
            try {
                Class<?> cipherClass = XposedHelpers.findClass(
                    "javax.crypto.Cipher", lpparam.classLoader);
                
                XposedHelpers.findAndHookMethod(cipherClass, "doFinal", 
                    byte[].class, new XC_MethodHook() {
                    @Override
                    protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                        byte[] input = (byte[]) param.args[0];
                        XposedBridge.log("[" + TAG + "] 加密操作，输入长度: " + 
                            (input != null ? input.length : 0));
                    }
                    
                    @Override
                    protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                        byte[] result = (byte[]) param.getResult();
                        XposedBridge.log("[" + TAG + "] 加密操作完成，输出长度: " + 
                            (result != null ? result.length : 0));
                    }
                });
            } catch (Throwable e) {
                XposedBridge.log("[" + TAG + "] Cipher Hook失败: " + e.getMessage());
            }
            
        } catch (Exception e) {
            XposedBridge.log("[" + TAG + "] Hook加密方法失败: " + e.getMessage());
        }
    }
    
    /**
     * Hook 应用验证
     */
    private static void hookAppValidation(XC_LoadPackage.LoadPackageParam lpparam) {
        try {
            // Hook PackageManager相关验证
            try {
                Class<?> packageManagerClass = XposedHelpers.findClass(
                    "android.content.pm.PackageManager", lpparam.classLoader);
                
                XposedHelpers.findAndHookMethod(packageManagerClass, "getPackageInfo",
                    String.class, int.class, new XC_MethodHook() {
                    @Override
                    protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                        String packageName = (String) param.args[0];
                        int flags = (Integer) param.args[1];
                        XposedBridge.log("[" + TAG + "] 获取包信息: " + packageName + ", flags: " + flags);
                    }
                });
            } catch (Throwable e) {
                XposedBridge.log("[" + TAG + "] PackageManager Hook失败: " + e.getMessage());
            }
            
        } catch (Exception e) {
            XposedBridge.log("[" + TAG + "] Hook应用验证失败: " + e.getMessage());
        }
    }
}
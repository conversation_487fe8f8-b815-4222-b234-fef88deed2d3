# NanoHTTPD端口冲突解决方案

## 🚨 问题分析

根据您提供的logcat日志，问题出现在：

```
java.net.BindException: bind failed: EADDRINUSE (Address already in use)
at fi.iki.elonen.NanoHTTPD$ServerRunnable.run(NanoHTTPD.java:1761)
```

**问题原因**：
- 模块 `com.call.taobao.spdy.HookLoader` 尝试启动NanoHTTPD服务器
- 目标端口已被其他进程或服务占用
- 导致Socket绑定失败

## 🔧 解决方案

### 方案1：动态端口分配（推荐）

在您的Xposed模块中实现智能端口选择：

```java
public class HookLoader implements IXposedHookLoadPackage {
    private static final String TAG = "TaobaoHook";
    private NanoHTTPD server;
    private static final int[] CANDIDATE_PORTS = {8080, 8081, 8082, 8090, 9000, 9001, 9002};
    
    @Override
    public void handleLoadPackage(XC_LoadPackage.LoadPackageParam lpparam) {
        if (lpparam.packageName.equals("com.taobao.taobao")) {
            startHttpServerWithRetry();
        }
    }
    
    private void startHttpServerWithRetry() {
        for (int port : CANDIDATE_PORTS) {
            try {
                server = new MyNanoHTTPD(port);
                server.start();
                Log.i(TAG, "✅ HTTP服务器启动成功: http://localhost:" + port);
                return; // 成功启动，退出循环
            } catch (java.net.BindException e) {
                Log.w(TAG, "⚠️ 端口 " + port + " 被占用，尝试下一个端口");
            } catch (Exception e) {
                Log.e(TAG, "❌ 端口 " + port + " 启动失败: " + e.getMessage());
            }
        }
        Log.e(TAG, "❌ 所有候选端口都启动失败，HTTP服务器无法启动");
    }
}
```

### 方案2：使用随机端口

```java
private void startHttpServerWithRandomPort() {
    for (int attempt = 0; attempt < 10; attempt++) {
        try {
            // 使用8000-9999范围内的随机端口
            int randomPort = 8000 + (int)(Math.random() * 2000);
            server = new MyNanoHTTPD(randomPort);
            server.start();
            Log.i(TAG, "✅ HTTP服务器启动成功: http://localhost:" + randomPort);
            return;
        } catch (java.net.BindException e) {
            Log.w(TAG, "⚠️ 随机端口被占用，重试中... (" + (attempt + 1) + "/10)");
        } catch (Exception e) {
            Log.e(TAG, "❌ 启动失败: " + e.getMessage());
            break;
        }
    }
    Log.e(TAG, "❌ 随机端口重试失败，HTTP服务器无法启动");
}
```

### 方案3：端口检测和释放

```java
private boolean isPortAvailable(int port) {
    try (java.net.ServerSocket socket = new java.net.ServerSocket(port)) {
        socket.setReuseAddress(true);
        return true;
    } catch (Exception e) {
        return false;
    }
}

private void startHttpServerSafely() {
    int targetPort = 8080;
    
    // 检查端口可用性
    if (!isPortAvailable(targetPort)) {
        Log.w(TAG, "⚠️ 端口 " + targetPort + " 不可用，查找可用端口...");
        
        // 查找可用端口
        for (int port = 8080; port <= 8200; port++) {
            if (isPortAvailable(port)) {
                targetPort = port;
                break;
            }
        }
    }
    
    try {
        server = new MyNanoHTTPD(targetPort);
        server.start();
        Log.i(TAG, "✅ HTTP服务器启动成功: http://localhost:" + targetPort);
    } catch (Exception e) {
        Log.e(TAG, "❌ HTTP服务器启动失败: " + e.getMessage());
    }
}
```

### 方案4：延迟启动和重试机制

```java
private void startHttpServerWithDelay() {
    // 延迟启动，避免与其他服务冲突
    new Thread(() -> {
        try {
            Thread.sleep(2000); // 延迟2秒
            
            for (int retry = 0; retry < 5; retry++) {
                try {
                    server = new MyNanoHTTPD(8080 + retry);
                    server.start();
                    Log.i(TAG, "✅ HTTP服务器延迟启动成功: http://localhost:" + (8080 + retry));
                    return;
                } catch (java.net.BindException e) {
                    Log.w(TAG, "⚠️ 重试 " + (retry + 1) + "/5: 端口被占用");
                    Thread.sleep(1000); // 等待1秒后重试
                }
            }
            Log.e(TAG, "❌ 延迟启动重试失败");
        } catch (InterruptedException e) {
            Log.e(TAG, "❌ 延迟启动被中断");
        }
    }).start();
}
```

## 🛠️ 调试和监控

### 端口占用检查

```bash
# 在adb shell中检查端口占用
adb shell netstat -tulpn | grep :8080

# 或者使用ss命令
adb shell ss -tulpn | grep :8080
```

### 日志监控

```bash
# 监控NanoHTTPD相关日志
adb logcat | grep -E "(NanoHTTPD|BindException|EADDRINUSE)"

# 监控特定模块日志
adb logcat | grep "TaobaoHook"
```

## 🔍 最佳实践

1. **使用高端口号**：避免使用1024以下的系统保留端口
2. **实现重试机制**：端口冲突时自动尝试其他端口
3. **添加端口检测**：启动前检查端口可用性
4. **优雅关闭**：应用退出时正确关闭HTTP服务器
5. **日志记录**：详细记录端口使用情况便于调试

## 📱 针对您的情况

基于日志显示的错误，建议：

1. **立即解决**：修改 `com.call.taobao.spdy.HookLoader` 使用动态端口分配
2. **长期优化**：实现完整的端口管理机制
3. **监控改进**：添加端口冲突的详细日志记录

## ⚠️ 注意事项

- 确保目标应用有INTERNET权限
- 避免在主线程中进行网络操作
- 考虑SELinux策略对网络访问的限制
- 测试不同Android版本的兼容性

## 🔗 相关文档

- [NanoHTTPD使用指南](./NANOHTTPD_USAGE_GUIDE.md)
- [Xposed模块开发文档](./XPOSED_MODULE_README.md)
- [网络权限处理指南](./NETWORK_PERMISSION_GUIDE.md)
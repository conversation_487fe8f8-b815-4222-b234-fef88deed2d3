package de.robv.android.xposed;

/**
 * IXposedHookZygoteInit - Zygote初始化Hook接口
 * 标准Xposed API兼容实现
 */
public interface IXposedHookZygoteInit {
    
    /**
     * 在Zygote进程中初始化时调用
     */
    void initZygote(StartupParam startupParam) throws Throwable;
    
    /**
     * 启动参数
     */
    class StartupParam {
        /** 模块路径 */
        public String modulePath;
        
        /** 是否启动系统服务器 */
        public boolean startsSystemServer;
    }
}
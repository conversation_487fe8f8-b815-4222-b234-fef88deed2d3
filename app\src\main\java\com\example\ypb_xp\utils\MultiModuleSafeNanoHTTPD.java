package com.example.ypb_xp.utils;

import android.util.Log;
import fi.iki.elonen.NanoHTTPD;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 多模块安全NanoHTTPD服务器
 * 专门为多模块环境设计的HTTP服务器，自动处理端口冲突和模块间协调
 * 
 * 功能特性：
 * - 集成GlobalPortCoordinator进行全局端口管理
 * - 支持ModulePortRangeManager的端口范围分配
 * - 自动端口冲突检测和重试
 * - 模块标识和状态监控
 * - 优雅的启动和停止机制
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class MultiModuleSafeNanoHTTPD extends NanoHTTPD {
    private static final String TAG = "MultiModuleSafeNanoHTTPD";
    
    private final String moduleName;
    private final int actualPort;
    private final long startTime;
    private boolean isRunning = false;
    
    // 服务器统计信息
    private int requestCount = 0;
    private long lastRequestTime = 0;
    
    /**
     * 受保护的构造函数，允许子类继承
     */
    protected MultiModuleSafeNanoHTTPD(String moduleName, int port) {
        super(port);
        this.moduleName = moduleName;
        this.actualPort = port;
        this.startTime = System.currentTimeMillis();
    }
    
    /**
     * 为指定模块创建安全的HTTP服务器
     * 
     * @param moduleName 模块名称
     * @param preferredPort 首选端口
     * @return 创建并启动的服务器实例，失败返回null
     */
    public static MultiModuleSafeNanoHTTPD createForModule(String moduleName, int preferredPort) {
        Log.i(TAG, "🚀 开始为模块 " + moduleName + " 创建HTTP服务器，首选端口: " + preferredPort);
        
        // 步骤1: 尝试全局端口协调分配
        int allocatedPort = GlobalPortCoordinator.allocatePortForModule(moduleName, preferredPort);
        
        if (allocatedPort == -1) {
            Log.w(TAG, "⚠️ 全局端口协调分配失败，尝试模块端口范围分配");
            
            // 步骤2: 尝试模块端口范围分配
            allocatedPort = ModulePortRangeManager.getPortForModule(moduleName, preferredPort);
        }
        
        if (allocatedPort == -1) {
            Log.e(TAG, "❌ 无法为模块 " + moduleName + " 分配端口");
            return null;
        }
        
        // 步骤3: 创建并启动服务器
        try {
            MultiModuleSafeNanoHTTPD server = new MultiModuleSafeNanoHTTPD(moduleName, allocatedPort);
            server.start();
            server.isRunning = true;
            
            Log.i(TAG, "✅ 模块 " + moduleName + " 的HTTP服务器启动成功");
            Log.i(TAG, "🌐 服务器地址: http://127.0.0.1:" + allocatedPort);
            Log.i(TAG, "📊 服务器状态: " + server.getServerStatus());
            
            return server;
            
        } catch (IOException e) {
            Log.e(TAG, "❌ 模块 " + moduleName + " 启动HTTP服务器失败: " + e.getMessage());
            
            // 启动失败时释放已分配的端口
            GlobalPortCoordinator.releasePortForModule(moduleName);
            return null;
        }
    }
    
    /**
     * 快速创建并启动服务器的便捷方法
     * 
     * @param moduleName 模块名称
     * @param preferredPort 首选端口
     * @return 服务器访问URL，失败返回null
     */
    public static String createAndStartQuick(String moduleName, int preferredPort) {
        MultiModuleSafeNanoHTTPD server = createForModule(moduleName, preferredPort);
        return server != null ? server.getServerUrl() : null;
    }
    
    @Override
    public void start() throws IOException {
        super.start();
        Log.i(TAG, "🎉 模块 " + moduleName + " 的HTTP服务器已启动，端口: " + actualPort);
    }
    
    @Override
    public void stop() {
        if (isRunning) {
            super.stop();
            isRunning = false;
            
            // 释放端口资源
            GlobalPortCoordinator.releasePortForModule(moduleName);
            
            Log.i(TAG, "🛑 模块 " + moduleName + " 的HTTP服务器已停止，端口已释放: " + actualPort);
            Log.i(TAG, "📈 服务器运行统计: 运行时长 " + getUptimeSeconds() + "秒, 处理请求 " + requestCount + " 次");
        }
    }
    
    @Override
    public Response serve(IHTTPSession session) {
        // 更新请求统计
        requestCount++;
        lastRequestTime = System.currentTimeMillis();
        
        String uri = session.getUri();
        String method = session.getMethod().toString();
        
        Log.d(TAG, "📥 模块 " + moduleName + " 收到请求: " + method + " " + uri);
        
        // 添加通用响应头
        Map<String, String> headers = new HashMap<>();
        headers.put("X-Module-Name", moduleName);
        headers.put("X-Server-Port", String.valueOf(actualPort));
        headers.put("X-Request-Count", String.valueOf(requestCount));
        headers.put("X-Server-Uptime", String.valueOf(getUptimeSeconds()));
        headers.put("Access-Control-Allow-Origin", "*");
        headers.put("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
        headers.put("Access-Control-Allow-Headers", "Content-Type, Authorization");
        
        // 处理预检请求
        if (Method.OPTIONS.equals(session.getMethod())) {
            return newFixedLengthResponse(Response.Status.OK, "text/plain", "");
        }
        
        // 路由处理
        switch (uri) {
            case "/":
            case "/index":
                return handleIndexRequest(session, headers);
                
            case "/module-info":
                return handleModuleInfoRequest(session, headers);
                
            case "/health":
                return handleHealthRequest(session, headers);
                
            case "/status":
                return handleStatusRequest(session, headers);
                
            case "/stats":
                return handleStatsRequest(session, headers);
                
            case "/port-info":
                return handlePortInfoRequest(session, headers);
                
            default:
                return handleCustomRequest(session, headers);
        }
    }
    
    /**
     * 处理首页请求
     */
    private Response handleIndexRequest(IHTTPSession session, Map<String, String> headers) {
        String html = String.format(
            "<html><head><title>%s HTTP Server</title></head><body>" +
            "<h1>%s HTTP Server</h1>" +
            "<p><strong>模块名称:</strong> %s</p>" +
            "<p><strong>服务器端口:</strong> %d</p>" +
            "<p><strong>运行时长:</strong> %d 秒</p>" +
            "<p><strong>处理请求:</strong> %d 次</p>" +
            "<h2>可用接口:</h2>" +
            "<ul>" +
            "<li><a href='/module-info'>/module-info</a> - 模块信息</li>" +
            "<li><a href='/health'>/health</a> - 健康检查</li>" +
            "<li><a href='/status'>/status</a> - 服务器状态</li>" +
            "<li><a href='/stats'>/stats</a> - 统计信息</li>" +
            "<li><a href='/port-info'>/port-info</a> - 端口信息</li>" +
            "</ul>" +
            "</body></html>",
            moduleName, moduleName, moduleName, actualPort, getUptimeSeconds(), requestCount
        );
        
        Response response = newFixedLengthResponse(Response.Status.OK, "text/html", html);
        for (Map.Entry<String, String> header : headers.entrySet()) {
            response.addHeader(header.getKey(), header.getValue());
        }
        return response;
    }
    
    /**
     * 处理模块信息请求
     */
    private Response handleModuleInfoRequest(IHTTPSession session, Map<String, String> headers) {
        String json = String.format(
            "{\"module\": \"%s\", \"port\": %d, \"status\": \"running\", \"uptime\": %d, \"requests\": %d, \"url\": \"%s\"}",
            moduleName, actualPort, getUptimeSeconds(), requestCount, getServerUrl()
        );
        
        Response response = newFixedLengthResponse(Response.Status.OK, "application/json", json);
        for (Map.Entry<String, String> header : headers.entrySet()) {
            response.addHeader(header.getKey(), header.getValue());
        }
        return response;
    }
    
    /**
     * 处理健康检查请求
     */
    private Response handleHealthRequest(IHTTPSession session, Map<String, String> headers) {
        String json = String.format(
            "{\"status\": \"healthy\", \"module\": \"%s\", \"port\": %d, \"timestamp\": %d}",
            moduleName, actualPort, System.currentTimeMillis()
        );
        
        Response response = newFixedLengthResponse(Response.Status.OK, "application/json", json);
        for (Map.Entry<String, String> header : headers.entrySet()) {
            response.addHeader(header.getKey(), header.getValue());
        }
        return response;
    }
    
    /**
     * 处理状态请求
     */
    private Response handleStatusRequest(IHTTPSession session, Map<String, String> headers) {
        String status = getDetailedServerStatus();
        
        Response response = newFixedLengthResponse(Response.Status.OK, "text/plain", status);
        for (Map.Entry<String, String> header : headers.entrySet()) {
            response.addHeader(header.getKey(), header.getValue());
        }
        return response;
    }
    
    /**
     * 处理统计信息请求
     */
    private Response handleStatsRequest(IHTTPSession session, Map<String, String> headers) {
        String stats = String.format(
            "模块统计信息:\n" +
            "模块名称: %s\n" +
            "服务器端口: %d\n" +
            "启动时间: %s\n" +
            "运行时长: %d 秒\n" +
            "总请求数: %d\n" +
            "最后请求时间: %s\n" +
            "平均请求频率: %.2f 请求/分钟\n",
            moduleName,
            actualPort,
            new java.util.Date(startTime).toString(),
            getUptimeSeconds(),
            requestCount,
            lastRequestTime > 0 ? new java.util.Date(lastRequestTime).toString() : "无",
            getRequestsPerMinute()
        );
        
        Response response = newFixedLengthResponse(Response.Status.OK, "text/plain", stats);
        for (Map.Entry<String, String> header : headers.entrySet()) {
            response.addHeader(header.getKey(), header.getValue());
        }
        return response;
    }
    
    /**
     * 处理端口信息请求
     */
    private Response handlePortInfoRequest(IHTTPSession session, Map<String, String> headers) {
        StringBuilder info = new StringBuilder();
        info.append("端口分配信息:\n");
        info.append("当前模块: ").append(moduleName).append("\n");
        info.append("分配端口: ").append(actualPort).append("\n\n");
        
        // 添加全局端口协调器信息
        info.append(GlobalPortCoordinator.getHealthReport()).append("\n");
        
        // 添加模块端口范围信息
        info.append(ModulePortRangeManager.getModuleRangeUsage(moduleName));
        
        Response response = newFixedLengthResponse(Response.Status.OK, "text/plain", info.toString());
        for (Map.Entry<String, String> header : headers.entrySet()) {
            response.addHeader(header.getKey(), header.getValue());
        }
        return response;
    }
    
    /**
     * 处理自定义请求（子类可以重写此方法）
     */
    protected Response handleCustomRequest(IHTTPSession session, Map<String, String> headers) {
        String message = String.format(
            "模块 %s 运行在端口 %d\n" +
            "请求路径: %s\n" +
            "请求方法: %s\n" +
            "\n此路径暂未实现，请查看可用接口:\n" +
            "- /module-info (模块信息)\n" +
            "- /health (健康检查)\n" +
            "- /status (服务器状态)\n" +
            "- /stats (统计信息)\n" +
            "- /port-info (端口信息)",
            moduleName, actualPort, session.getUri(), session.getMethod()
        );
        
        Response response = newFixedLengthResponse(Response.Status.NOT_FOUND, "text/plain", message);
        for (Map.Entry<String, String> header : headers.entrySet()) {
            response.addHeader(header.getKey(), header.getValue());
        }
        return response;
    }
    
    /**
     * 获取服务器URL
     */
    public String getServerUrl() {
        return "http://127.0.0.1:" + actualPort;
    }
    
    /**
     * 获取模块名称
     */
    public String getModuleName() {
        return moduleName;
    }
    
    /**
     * 获取实际端口
     */
    public int getActualPort() {
        return actualPort;
    }
    
    /**
     * 获取运行时长（秒）
     */
    public long getUptimeSeconds() {
        return (System.currentTimeMillis() - startTime) / 1000;
    }
    
    /**
     * 获取请求数
     */
    public int getRequestCount() {
        return requestCount;
    }
    
    /**
     * 获取每分钟请求数
     */
    public double getRequestsPerMinute() {
        long uptimeMinutes = getUptimeSeconds() / 60;
        return uptimeMinutes > 0 ? (double) requestCount / uptimeMinutes : 0;
    }
    
    /**
     * 检查服务器是否正在运行
     */
    public boolean isRunning() {
        return isRunning;
    }
    
    /**
     * 获取服务器状态
     */
    public String getServerStatus() {
        return String.format("模块:%s, 端口:%d, 状态:%s, 运行时长:%ds, 请求数:%d",
                moduleName, actualPort, isRunning ? "运行中" : "已停止", getUptimeSeconds(), requestCount);
    }
    
    /**
     * 获取详细的服务器状态
     */
    public String getDetailedServerStatus() {
        return String.format(
            "=== MultiModuleSafeNanoHTTPD 服务器状态 ===\n" +
            "模块名称: %s\n" +
            "服务器端口: %d\n" +
            "服务器地址: %s\n" +
            "运行状态: %s\n" +
            "启动时间: %s\n" +
            "运行时长: %d 秒\n" +
            "总请求数: %d\n" +
            "最后请求: %s\n" +
            "请求频率: %.2f 请求/分钟\n" +
            "=== 状态报告结束 ===",
            moduleName,
            actualPort,
            getServerUrl(),
            isRunning ? "运行中" : "已停止",
            new java.util.Date(startTime).toString(),
            getUptimeSeconds(),
            requestCount,
            lastRequestTime > 0 ? new java.util.Date(lastRequestTime).toString() : "无",
            getRequestsPerMinute()
        );
    }
    
    /**
     * 打印服务器状态到日志
     */
    public void printStatus() {
        Log.i(TAG, getDetailedServerStatus());
    }
}
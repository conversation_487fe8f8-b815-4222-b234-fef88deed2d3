package com.example.ypb_xp.utils;

import android.util.Log;
import java.util.HashMap;
import java.util.Map;

/**
 * 模块端口范围管理器
 * 为不同的Xposed模块分配专用的端口范围，避免模块间端口冲突
 * 
 * 功能特性：
 * - 预定义模块端口范围
 * - 动态端口范围分配
 * - 端口范围冲突检测
 * - 模块端口使用统计
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ModulePortRangeManager {
    private static final String TAG = "ModulePortRangeManager";
    
    // 预定义的模块端口范围映射
    private static final Map<String, PortRange> MODULE_PORT_RANGES = new HashMap<>();
    
    // 动态分配的端口范围
    private static final Map<String, PortRange> DYNAMIC_PORT_RANGES = new HashMap<>();
    
    // 下一个可用的动态端口范围起始点
    private static int nextDynamicRangeStart = 9000;
    
    // 每个动态范围的大小
    private static final int DYNAMIC_RANGE_SIZE = 20;
    
    static {
        // 初始化预定义的模块端口范围
        initializePredefinedRanges();
    }
    
    /**
     * 初始化预定义的模块端口范围
     */
    private static void initializePredefinedRanges() {
        // 淘宝相关模块
        MODULE_PORT_RANGES.put("com.call.taobao.spdy", new PortRange(8080, 8099, "淘宝SPDY Hook模块"));
        MODULE_PORT_RANGES.put("com.bypass.ali.spdy", new PortRange(8100, 8119, "阿里SPDY绕过模块"));
        MODULE_PORT_RANGES.put("com.taobao.hook", new PortRange(8120, 8139, "淘宝通用Hook模块"));
        
        // 本项目模块
        MODULE_PORT_RANGES.put("com.example.ypb_xp", new PortRange(8140, 8159, "YPB_XP主模块"));
        MODULE_PORT_RANGES.put("com.example.ypb_xp.yuanluobo", new PortRange(8160, 8179, "元萝卜Hook模块"));
        
        // 其他常见模块
        MODULE_PORT_RANGES.put("com.android.hook", new PortRange(8180, 8199, "Android通用Hook"));
        MODULE_PORT_RANGES.put("com.security.bypass", new PortRange(8200, 8219, "安全绕过模块"));
        MODULE_PORT_RANGES.put("com.network.intercept", new PortRange(8220, 8239, "网络拦截模块"));
        
        // 调试和测试模块
        MODULE_PORT_RANGES.put("com.debug.helper", new PortRange(8240, 8259, "调试辅助模块"));
        MODULE_PORT_RANGES.put("com.test.module", new PortRange(8260, 8279, "测试模块"));
        
        Log.i(TAG, "✅ 已初始化 " + MODULE_PORT_RANGES.size() + " 个预定义模块端口范围");
    }
    
    /**
     * 为模块获取端口
     * 
     * @param moduleName 模块名称
     * @param preferredPort 首选端口（可选，用于验证是否在分配范围内）
     * @return 分配的端口，-1表示分配失败
     */
    public static int getPortForModule(String moduleName, int preferredPort) {
        PortRange range = getPortRangeForModule(moduleName);
        
        if (range == null) {
            Log.e(TAG, "❌ 无法为模块 " + moduleName + " 获取端口范围");
            return -1;
        }
        
        Log.i(TAG, "🔍 为模块 " + moduleName + " 在范围 " + range + " 内查找可用端口");
        
        // 如果指定了首选端口，先检查是否在范围内且可用
        if (preferredPort > 0 && range.contains(preferredPort)) {
            if (PortManager.isPortAvailable(preferredPort)) {
                Log.i(TAG, "✅ 首选端口 " + preferredPort + " 可用，分配给模块 " + moduleName);
                return preferredPort;
            } else {
                Log.w(TAG, "⚠️ 首选端口 " + preferredPort + " 被占用，寻找替代端口");
            }
        }
        
        // 在指定范围内查找可用端口
        for (int port = range.start; port <= range.end; port++) {
            if (PortManager.isPortAvailable(port)) {
                Log.i(TAG, "✅ 为模块 " + moduleName + " 分配端口: " + port + " (范围: " + range + ")");
                return port;
            }
        }
        
        Log.e(TAG, "❌ 模块 " + moduleName + " 在范围 " + range + " 内无可用端口");
        return -1;
    }
    
    /**
     * 获取模块的端口范围
     * 
     * @param moduleName 模块名称
     * @return 端口范围，如果不存在则动态分配
     */
    public static PortRange getPortRangeForModule(String moduleName) {
        // 首先检查预定义范围
        PortRange predefinedRange = MODULE_PORT_RANGES.get(moduleName);
        if (predefinedRange != null) {
            Log.d(TAG, "使用预定义范围: " + moduleName + " -> " + predefinedRange);
            return predefinedRange;
        }
        
        // 检查是否已有动态分配的范围
        PortRange dynamicRange = DYNAMIC_PORT_RANGES.get(moduleName);
        if (dynamicRange != null) {
            Log.d(TAG, "使用已分配的动态范围: " + moduleName + " -> " + dynamicRange);
            return dynamicRange;
        }
        
        // 动态分配新的端口范围
        return allocateDynamicRange(moduleName);
    }
    
    /**
     * 为模块动态分配端口范围
     * 
     * @param moduleName 模块名称
     * @return 新分配的端口范围
     */
    private static PortRange allocateDynamicRange(String moduleName) {
        synchronized (ModulePortRangeManager.class) {
            int start = nextDynamicRangeStart;
            int end = start + DYNAMIC_RANGE_SIZE - 1;
            
            // 确保不超过有效端口范围
            if (end > 65535) {
                Log.e(TAG, "❌ 无法为模块 " + moduleName + " 分配动态端口范围：超出有效范围");
                return null;
            }
            
            PortRange newRange = new PortRange(start, end, "动态分配给 " + moduleName);
            DYNAMIC_PORT_RANGES.put(moduleName, newRange);
            
            // 更新下一个可用范围的起始点
            nextDynamicRangeStart = end + 1;
            
            Log.i(TAG, "🆕 为模块 " + moduleName + " 动态分配端口范围: " + newRange);
            return newRange;
        }
    }
    
    /**
     * 添加自定义模块端口范围
     * 
     * @param moduleName 模块名称
     * @param start 起始端口
     * @param end 结束端口
     * @param description 描述
     * @return 是否添加成功
     */
    public static boolean addCustomModuleRange(String moduleName, int start, int end, String description) {
        if (start <= 0 || end <= 0 || start > end || end > 65535) {
            Log.e(TAG, "❌ 无效的端口范围: " + start + "-" + end);
            return false;
        }
        
        // 检查是否与现有范围冲突
        PortRange newRange = new PortRange(start, end, description);
        if (isRangeConflict(newRange)) {
            Log.e(TAG, "❌ 端口范围冲突: " + newRange);
            return false;
        }
        
        MODULE_PORT_RANGES.put(moduleName, newRange);
        Log.i(TAG, "✅ 添加自定义模块端口范围: " + moduleName + " -> " + newRange);
        return true;
    }
    
    /**
     * 检查端口范围是否与现有范围冲突
     * 
     * @param newRange 新的端口范围
     * @return true表示有冲突，false表示无冲突
     */
    private static boolean isRangeConflict(PortRange newRange) {
        for (PortRange existingRange : MODULE_PORT_RANGES.values()) {
            if (newRange.overlaps(existingRange)) {
                Log.w(TAG, "端口范围冲突: " + newRange + " 与 " + existingRange + " 重叠");
                return true;
            }
        }
        
        for (PortRange existingRange : DYNAMIC_PORT_RANGES.values()) {
            if (newRange.overlaps(existingRange)) {
                Log.w(TAG, "端口范围冲突: " + newRange + " 与动态范围 " + existingRange + " 重叠");
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 获取所有模块的端口范围信息
     * 
     * @return 端口范围信息字符串
     */
    public static String getAllModuleRanges() {
        StringBuilder sb = new StringBuilder();
        sb.append("=== 模块端口范围分配 ===\n");
        
        sb.append("\n预定义范围 (").append(MODULE_PORT_RANGES.size()).append(" 个):\n");
        for (Map.Entry<String, PortRange> entry : MODULE_PORT_RANGES.entrySet()) {
            sb.append("  ").append(entry.getKey()).append(" -> ").append(entry.getValue()).append("\n");
        }
        
        if (!DYNAMIC_PORT_RANGES.isEmpty()) {
            sb.append("\n动态分配范围 (").append(DYNAMIC_PORT_RANGES.size()).append(" 个):\n");
            for (Map.Entry<String, PortRange> entry : DYNAMIC_PORT_RANGES.entrySet()) {
                sb.append("  ").append(entry.getKey()).append(" -> ").append(entry.getValue()).append("\n");
            }
        }
        
        sb.append("\n下一个动态范围起始: ").append(nextDynamicRangeStart).append("\n");
        sb.append("=== 范围分配结束 ===");
        
        return sb.toString();
    }
    
    /**
     * 打印所有模块端口范围
     */
    public static void printAllModuleRanges() {
        Log.i(TAG, getAllModuleRanges());
    }
    
    /**
     * 检查模块是否有预定义的端口范围
     * 
     * @param moduleName 模块名称
     * @return true表示有预定义范围，false表示需要动态分配
     */
    public static boolean hasPredefinedRange(String moduleName) {
        return MODULE_PORT_RANGES.containsKey(moduleName);
    }
    
    /**
     * 获取模块端口范围的使用统计
     * 
     * @param moduleName 模块名称
     * @return 使用统计信息
     */
    public static String getModuleRangeUsage(String moduleName) {
        PortRange range = getPortRangeForModule(moduleName);
        if (range == null) {
            return "模块 " + moduleName + " 无分配的端口范围";
        }
        
        int totalPorts = range.size();
        int availablePorts = 0;
        
        for (int port = range.start; port <= range.end; port++) {
            if (PortManager.isPortAvailable(port)) {
                availablePorts++;
            }
        }
        
        int usedPorts = totalPorts - availablePorts;
        double usagePercentage = (double) usedPorts / totalPorts * 100;
        
        return String.format("模块 %s 端口使用情况:\n" +
                "  范围: %s\n" +
                "  总端口数: %d\n" +
                "  已使用: %d\n" +
                "  可用: %d\n" +
                "  使用率: %.1f%%",
                moduleName, range, totalPorts, usedPorts, availablePorts, usagePercentage);
    }
    
    /**
     * 端口范围类
     */
    public static class PortRange {
        public final int start;
        public final int end;
        public final String description;
        
        public PortRange(int start, int end, String description) {
            this.start = start;
            this.end = end;
            this.description = description != null ? description : "";
        }
        
        /**
         * 检查端口是否在此范围内
         */
        public boolean contains(int port) {
            return port >= start && port <= end;
        }
        
        /**
         * 检查与另一个范围是否重叠
         */
        public boolean overlaps(PortRange other) {
            return !(this.end < other.start || this.start > other.end);
        }
        
        /**
         * 获取范围大小
         */
        public int size() {
            return end - start + 1;
        }
        
        @Override
        public String toString() {
            return start + "-" + end + (description.isEmpty() ? "" : " (" + description + ")");
        }
        
        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;
            PortRange portRange = (PortRange) obj;
            return start == portRange.start && end == portRange.end;
        }
        
        @Override
        public int hashCode() {
            return start * 31 + end;
        }
    }
}
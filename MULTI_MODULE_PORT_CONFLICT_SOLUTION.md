# 多模块环境下的NanoHTTPD端口冲突解决方案

## 🎯 问题分析

根据最新的logcat日志，我们发现了一个多模块环境下的端口冲突问题：

### 问题现象
```
08-01 18:02:21.201 E XposedModuleLoader: java.net.BindException: bind failed: EADDRINUSE (Address already in use)
08-01 18:02:21.203 I XposedModuleLoader: 模块应用成功: com.call.taobao.spdy
08-01 18:02:21.203 I XposedModuleLoader: 模块适用，开始应用到克隆APP: com.bypass.ali.spdy
```

### 关键发现
1. **多个模块同时运行**: `com.call.taobao.spdy` 和 `com.bypass.ali.spdy`
2. **端口冲突但模块仍然成功**: 尽管出现BindException，系统仍显示"模块应用成功"
3. **克隆应用环境**: 目标是 `com.taobao.taobao_clone_144f50a8`

## 🔧 多模块端口管理解决方案

### 方案1: 全局端口协调器

创建一个全局的端口协调器，确保多个模块之间不会发生端口冲突：

```java
public class GlobalPortCoordinator {
    private static final String SHARED_PREFS_NAME = "xposed_port_coordinator";
    private static final Set<Integer> OCCUPIED_PORTS = new ConcurrentHashMap<Integer, Boolean>().keySet(ConcurrentHashMap.newKeySet());
    private static final Object LOCK = new Object();
    
    /**
     * 为模块分配唯一端口
     * @param moduleName 模块名称
     * @param preferredPort 首选端口
     * @return 分配的端口，-1表示分配失败
     */
    public static int allocatePortForModule(String moduleName, int preferredPort) {
        synchronized (LOCK) {
            // 检查首选端口是否可用
            if (isPortAvailable(preferredPort) && !OCCUPIED_PORTS.contains(preferredPort)) {
                OCCUPIED_PORTS.add(preferredPort);
                savePortAllocation(moduleName, preferredPort);
                Log.i("GlobalPortCoordinator", "为模块 " + moduleName + " 分配端口: " + preferredPort);
                return preferredPort;
            }
            
            // 寻找可用端口
            for (int port = preferredPort + 1; port <= preferredPort + 100; port++) {
                if (isPortAvailable(port) && !OCCUPIED_PORTS.contains(port)) {
                    OCCUPIED_PORTS.add(port);
                    savePortAllocation(moduleName, port);
                    Log.i("GlobalPortCoordinator", "为模块 " + moduleName + " 分配替代端口: " + port);
                    return port;
                }
            }
            
            Log.e("GlobalPortCoordinator", "无法为模块 " + moduleName + " 分配端口");
            return -1;
        }
    }
    
    /**
     * 释放模块占用的端口
     */
    public static void releasePortForModule(String moduleName) {
        synchronized (LOCK) {
            int port = getPortForModule(moduleName);
            if (port != -1) {
                OCCUPIED_PORTS.remove(port);
                removePortAllocation(moduleName);
                Log.i("GlobalPortCoordinator", "释放模块 " + moduleName + " 的端口: " + port);
            }
        }
    }
    
    /**
     * 获取模块当前使用的端口
     */
    public static int getPortForModule(String moduleName) {
        // 从SharedPreferences或内存中获取
        return -1; // 实现细节
    }
    
    private static boolean isPortAvailable(int port) {
        try (ServerSocket socket = new ServerSocket()) {
            socket.setReuseAddress(true);
            socket.bind(new InetSocketAddress("127.0.0.1", port));
            return true;
        } catch (IOException e) {
            return false;
        }
    }
    
    private static void savePortAllocation(String moduleName, int port) {
        // 保存到SharedPreferences或文件
    }
    
    private static void removePortAllocation(String moduleName) {
        // 从SharedPreferences或文件中移除
    }
}
```

### 方案2: 模块特定的端口范围

为不同的模块分配不同的端口范围：

```java
public class ModulePortRangeManager {
    private static final Map<String, PortRange> MODULE_PORT_RANGES = new HashMap<>();
    
    static {
        // 为不同模块分配专用端口范围
        MODULE_PORT_RANGES.put("com.call.taobao.spdy", new PortRange(8080, 8099));
        MODULE_PORT_RANGES.put("com.bypass.ali.spdy", new PortRange(8100, 8119));
        MODULE_PORT_RANGES.put("com.example.ypb_xp", new PortRange(8120, 8139));
        // 可以根据需要添加更多模块
    }
    
    public static int getPortForModule(String moduleName, int preferredPort) {
        PortRange range = MODULE_PORT_RANGES.get(moduleName);
        if (range == null) {
            // 使用默认范围
            range = new PortRange(9000, 9099);
            Log.w("ModulePortRange", "模块 " + moduleName + " 未配置专用端口范围，使用默认范围: " + range);
        }
        
        // 在指定范围内查找可用端口
        for (int port = range.start; port <= range.end; port++) {
            if (PortManager.isPortAvailable(port)) {
                Log.i("ModulePortRange", "为模块 " + moduleName + " 分配端口: " + port + " (范围: " + range + ")");
                return port;
            }
        }
        
        Log.e("ModulePortRange", "模块 " + moduleName + " 在范围 " + range + " 内无可用端口");
        return -1;
    }
    
    private static class PortRange {
        final int start;
        final int end;
        
        PortRange(int start, int end) {
            this.start = start;
            this.end = end;
        }
        
        @Override
        public String toString() {
            return start + "-" + end;
        }
    }
}
```

### 方案3: 智能端口检测和重试

增强现有的SafeNanoHTTPD，添加多模块环境下的智能检测：

```java
public class MultiModuleSafeNanoHTTPD extends NanoHTTPD {
    private static final String TAG = "MultiModuleSafeNanoHTTPD";
    private final String moduleName;
    private int actualPort;
    
    private MultiModuleSafeNanoHTTPD(String moduleName, int port) {
        super(port);
        this.moduleName = moduleName;
        this.actualPort = port;
    }
    
    public static MultiModuleSafeNanoHTTPD createForModule(String moduleName, int preferredPort) {
        // 首先尝试全局端口协调
        int allocatedPort = GlobalPortCoordinator.allocatePortForModule(moduleName, preferredPort);
        if (allocatedPort == -1) {
            // 如果全局协调失败，尝试模块特定范围
            allocatedPort = ModulePortRangeManager.getPortForModule(moduleName, preferredPort);
        }
        
        if (allocatedPort == -1) {
            Log.e(TAG, "无法为模块 " + moduleName + " 分配端口");
            return null;
        }
        
        try {
            MultiModuleSafeNanoHTTPD server = new MultiModuleSafeNanoHTTPD(moduleName, allocatedPort);
            server.start();
            Log.i(TAG, "模块 " + moduleName + " 的HTTP服务器启动成功，端口: " + allocatedPort);
            return server;
        } catch (IOException e) {
            Log.e(TAG, "模块 " + moduleName + " 启动HTTP服务器失败: " + e.getMessage());
            GlobalPortCoordinator.releasePortForModule(moduleName);
            return null;
        }
    }
    
    @Override
    public void stop() {
        super.stop();
        GlobalPortCoordinator.releasePortForModule(moduleName);
        Log.i(TAG, "模块 " + moduleName + " 的HTTP服务器已停止，端口已释放: " + actualPort);
    }
    
    @Override
    public Response serve(IHTTPSession session) {
        // 添加模块标识信息
        Map<String, String> headers = new HashMap<>();
        headers.put("X-Module-Name", moduleName);
        headers.put("X-Server-Port", String.valueOf(actualPort));
        
        String uri = session.getUri();
        if ("/module-info".equals(uri)) {
            String info = String.format(
                "{\"module\": \"%s\", \"port\": %d, \"status\": \"running\"}",
                moduleName, actualPort
            );
            return newFixedLengthResponse(Response.Status.OK, "application/json", info);
        }
        
        return newFixedLengthResponse(Response.Status.OK, "text/plain", 
            "Module: " + moduleName + " running on port " + actualPort);
    }
}
```

## 🚀 实施指南

### 步骤1: 修改现有模块

对于 `com.call.taobao.spdy.HookLoader`，建议修改为：

```java
public class HookLoader implements IXposedHookLoadPackage {
    private MultiModuleSafeNanoHTTPD httpServer;
    
    @Override
    public void handleLoadPackage(XC_LoadPackage.LoadPackageParam lpparam) {
        if (lpparam.packageName.equals("com.taobao.taobao")) {
            startHttpServerSafely();
            // 其他Hook逻辑...
        }
    }
    
    private void startHttpServerSafely() {
        try {
            httpServer = MultiModuleSafeNanoHTTPD.createForModule(
                "com.call.taobao.spdy", 8080
            );
            if (httpServer != null) {
                Log.i("TaobaoHook", "HTTP服务器启动成功");
            }
        } catch (Exception e) {
            Log.e("TaobaoHook", "HTTP服务器启动失败: " + e.getMessage());
        }
    }
}
```

### 步骤2: 监控和调试

```bash
# 监控端口分配情况
adb logcat | grep -E "(GlobalPortCoordinator|ModulePortRange|MultiModuleSafeNanoHTTPD)"

# 检查端口占用
adb shell netstat -tulpn | grep -E ":(80[0-9][0-9]|90[0-9][0-9])"

# 测试模块信息接口
adb shell "curl http://127.0.0.1:8080/module-info 2>/dev/null || echo 'Port 8080 not available'"
adb shell "curl http://127.0.0.1:8100/module-info 2>/dev/null || echo 'Port 8100 not available'"
```

### 步骤3: 验证解决效果

成功实施后，您应该看到类似的日志：

```
I GlobalPortCoordinator: 为模块 com.call.taobao.spdy 分配端口: 8080
I MultiModuleSafeNanoHTTPD: 模块 com.call.taobao.spdy 的HTTP服务器启动成功，端口: 8080
I GlobalPortCoordinator: 为模块 com.bypass.ali.spdy 分配端口: 8100
I MultiModuleSafeNanoHTTPD: 模块 com.bypass.ali.spdy 的HTTP服务器启动成功，端口: 8100
```

## 🔍 故障排除

### 常见问题

1. **端口仍然冲突**
   - 检查是否有其他应用占用端口
   - 增加端口搜索范围
   - 使用随机端口策略

2. **模块间通信问题**
   - 实现模块发现机制
   - 使用统一的API接口
   - 添加健康检查端点

3. **性能影响**
   - 优化端口检测算法
   - 使用缓存减少重复检测
   - 异步启动HTTP服务器

### 高级配置

```java
// 配置模块优先级
GlobalPortCoordinator.setModulePriority("com.call.taobao.spdy", 1);
GlobalPortCoordinator.setModulePriority("com.bypass.ali.spdy", 2);

// 配置端口回收策略
GlobalPortCoordinator.setPortRecycleTimeout(30000); // 30秒后回收

// 启用端口使用统计
GlobalPortCoordinator.enablePortUsageStatistics(true);
```

## 📊 预期效果

实施此解决方案后：

✅ **消除端口冲突**: 多个模块可以同时运行而不会发生端口冲突
✅ **智能端口分配**: 自动为每个模块分配合适的端口
✅ **资源管理**: 自动回收不再使用的端口
✅ **监控和调试**: 提供详细的端口使用情况和模块状态信息
✅ **向后兼容**: 不影响现有模块的基本功能

---

**注意**: 此解决方案专门针对多模块环境下的端口冲突问题，可以与之前创建的单模块解决方案配合使用。
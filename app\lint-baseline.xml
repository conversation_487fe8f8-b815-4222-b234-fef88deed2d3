<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.11.1" type="baseline" client="gradle" dependencies="false" name="AGP (8.11.1)" variant="all" version="8.11.1">

    <issue
        id="ScopedStorage"
        message="The Google Play store has a policy that limits usage of MANAGE_EXTERNAL_STORAGE"
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.MANAGE_EXTERNAL_STORAGE&quot; />"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="9"
            column="36"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="        return String.format("
        errorLine2="               ^">
        <location
            file="src/main/java/com/example/ypb_xp/security/SecurityManager.java"
            line="290"
            column="16"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="        return String.format("
        errorLine2="               ^">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="479"
            column="16"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="            String payload = String.format("
        errorLine2="                             ^">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="530"
            column="30"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="            binding.moduleStats.setText(String.format(&quot;共 %d 个模块，已启用 %d 个&quot;, "
        errorLine2="                                        ^">
        <location
            file="src/main/java/com/example/ypb_xp/XposedModulesActivity.java"
            line="114"
            column="41"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="        String details = String.format("
        errorLine2="                         ^">
        <location
            file="src/main/java/com/example/ypb_xp/XposedModulesActivity.java"
            line="150"
            column="26"/>
    </issue>

    <issue
        id="DiscouragedPrivateApi"
        message="Reflective access to currentProcessName, which is not part of the public SDK and therefore likely to change in future Android releases"
        errorLine1="            Method currentProcessName = activityThread.getDeclaredMethod(&quot;currentProcessName&quot;);"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/HookInjector.java"
            line="162"
            column="41"/>
    </issue>

    <issue
        id="DiscouragedPrivateApi"
        message="Reflective access to theUnsafe, which is not part of the public SDK and therefore likely to change in future Android releases"
        errorLine1="            java.lang.reflect.Field theUnsafeField = unsafeClass.getDeclaredField(&quot;theUnsafe&quot;);"
        errorLine2="                                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="602"
            column="54"/>
    </issue>

    <issue
        id="DiscouragedPrivateApi"
        message="Reflective access to allocateInstance, which is not part of the public SDK and therefore likely to change in future Android releases"
        errorLine1="            java.lang.reflect.Method allocateInstance = unsafeClass.getDeclaredMethod(&quot;allocateInstance&quot;, Class.class);"
        errorLine2="                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="607"
            column="57"/>
    </issue>

    <issue
        id="PrivateApi"
        message="Accessing internal APIs via reflection is not supported and may not work on all devices or in the future"
        errorLine1="            Class&lt;?> contextImplClass = Class.forName(&quot;android.app.ContextImpl&quot;);"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="216"
            column="41"/>
    </issue>

    <issue
        id="PrivateApi"
        message="Accessing internal APIs via reflection is not supported and may not work on all devices or in the future"
        errorLine1="            Class&lt;?> activityThread = Class.forName(&quot;android.app.ActivityThread&quot;);"
        errorLine2="                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/HookInjector.java"
            line="161"
            column="39"/>
    </issue>

    <issue
        id="SdCardPath"
        message="Do not hardcode &quot;`/data/`&quot;; use `Context.getFilesDir().getPath()` instead"
        errorLine1="            String originalDataPath = &quot;/data/data/&quot; + packageName;"
        errorLine2="                                      ~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="322"
            column="39"/>
    </issue>

    <issue
        id="SdCardPath"
        message="Do not hardcode &quot;`/data/`&quot;; use `Context.getFilesDir().getPath()` instead"
        errorLine1="        this.dataPath = &quot;/data/data/com.example.ypb_xp/clones/&quot; + cloneId;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/model/ClonedApp.java"
            line="24"
            column="25"/>
    </issue>

    <issue
        id="SdCardPath"
        message="Do not hardcode &quot;`/data/`&quot;; use `Context.getFilesDir().getPath()` instead"
        errorLine1="            if (originalPath.startsWith(&quot;/data/data/&quot; + hostPackageName)) {"
        errorLine2="                                        ~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="249"
            column="41"/>
    </issue>

    <issue
        id="SdCardPath"
        message="Do not hardcode &quot;`/data/`&quot;; use `Context.getFilesDir().getPath()` instead"
        errorLine1="                    &quot;/data/data/&quot; + hostPackageName,"
        errorLine2="                    ~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="251"
            column="21"/>
    </issue>

    <issue
        id="SdCardPath"
        message="Do not hardcode &quot;`/data/`&quot;; use `Context.getFilesDir().getPath()` instead"
        errorLine1="                    &quot;/data/data/com.example.ypb_xp/virtual/&quot; + currentCloneId + &quot;/data&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="252"
            column="21"/>
    </issue>

    <issue
        id="SdCardPath"
        message="Do not hardcode &quot;/sdcard/&quot;; use `Environment.getExternalStorageDirectory().getPath()` instead"
        errorLine1="            if (originalPath.startsWith(&quot;/sdcard/Android/data/&quot; + hostPackageName)) {"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="267"
            column="41"/>
    </issue>

    <issue
        id="SdCardPath"
        message="Do not hardcode &quot;/sdcard/&quot;; use `Environment.getExternalStorageDirectory().getPath()` instead"
        errorLine1="                    &quot;/sdcard/Android/data/&quot; + hostPackageName,"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="269"
            column="21"/>
    </issue>

    <issue
        id="SdCardPath"
        message="Do not hardcode &quot;/sdcard/&quot;; use `Environment.getExternalStorageDirectory().getPath()` instead"
        errorLine1="                    &quot;/sdcard/Android/data/com.example.ypb_xp/virtual/&quot; + currentCloneId"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="270"
            column="21"/>
    </issue>

    <issue
        id="SdCardPath"
        message="Do not hardcode &quot;`/data/`&quot;; use `Context.getFilesDir().getPath()` instead"
        errorLine1="            String testPath = &quot;/data/data/&quot; + context.getPackageName() + &quot;/test.txt&quot;;"
        errorLine2="                              ~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/HookReceiver.java"
            line="234"
            column="31"/>
    </issue>

    <issue
        id="SdCardPath"
        message="Do not hardcode &quot;`/data/`&quot;; use `Context.getFilesDir().getPath()` instead"
        errorLine1="            String commandFilePath = &quot;/data/data/&quot; + packageName + &quot;/cache/hook_injection_command.txt&quot;;"
        errorLine2="                                     ~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/TargetAppHookReceiver.java"
            line="99"
            column="38"/>
    </issue>

    <issue
        id="SdCardPath"
        message="Do not hardcode &quot;`/data/`&quot;; use `Context.getFilesDir().getPath()` instead"
        errorLine1="        if (originalPath.startsWith(&quot;/data/data/&quot; + packageName)) {"
        errorLine2="                                    ~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualEnvironment.java"
            line="117"
            column="37"/>
    </issue>

    <issue
        id="SdCardPath"
        message="Do not hardcode &quot;`/data/`&quot;; use `Context.getFilesDir().getPath()` instead"
        errorLine1="            return originalPath.replace(&quot;/data/data/&quot; + packageName, dataPath);"
        errorLine2="                                        ~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualEnvironment.java"
            line="118"
            column="41"/>
    </issue>

    <issue
        id="SdCardPath"
        message="Do not hardcode &quot;/sdcard/&quot;; use `Environment.getExternalStorageDirectory().getPath()` instead"
        errorLine1="        } else if (originalPath.startsWith(&quot;/sdcard&quot;) || originalPath.startsWith(&quot;/storage/emulated/0&quot;)) {"
        errorLine2="                                           ~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualEnvironment.java"
            line="119"
            column="44"/>
    </issue>

    <issue
        id="SdCardPath"
        message="Do not hardcode &quot;/sdcard/&quot;; use `Environment.getExternalStorageDirectory().getPath()` instead"
        errorLine1="            return originalPath.replace(&quot;/sdcard&quot;, externalPath).replace(&quot;/storage/emulated/0&quot;, externalPath);"
        errorLine2="                                        ~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualEnvironment.java"
            line="120"
            column="41"/>
    </issue>

    <issue
        id="SdCardPath"
        message="Do not hardcode &quot;`/data/`&quot;; use `Context.getFilesDir().getPath()` instead"
        errorLine1="            String hookFlagPath = &quot;/data/user/0/com.example.ypb_xp/cache/immediate_hook_&quot; + "
        errorLine2="                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="652"
            column="35"/>
    </issue>

    <issue
        id="SdCardPath"
        message="Do not hardcode &quot;/sdcard/&quot;; use `Environment.getExternalStorageDirectory().getPath()` instead"
        errorLine1="            String publicDir = &quot;/sdcard/Android/data/&quot; + context.getPackageName() + &quot;/cache&quot;;"
        errorLine2="                               ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="790"
            column="32"/>
    </issue>

    <issue
        id="SdCardPath"
        message="Do not hardcode &quot;`/data/`&quot;; use `Context.getFilesDir().getPath()` instead"
        errorLine1="                String dataPath = &quot;/data/data/&quot; + getPackageName() + &quot;/virtual/&quot; + cloneId;"
        errorLine2="                                  ~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="139"
            column="35"/>
    </issue>

    <issue
        id="ProtectedPermissions"
        message="Permission is only granted to system apps"
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.BIND_DEVICE_ADMIN&quot; />"
        errorLine2="                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="26"
            column="22"/>
    </issue>

    <issue
        id="UnspecifiedRegisterReceiverFlag"
        message="`receiver` is missing `RECEIVER_EXPORTED` or `RECEIVER_NOT_EXPORTED` flag for unprotected broadcasts registered for com.example.ypb_xp.HOOK_INJECTION, com.example.ypb_xp.HOOK_TEST"
        errorLine1="            context.registerReceiver(receiver, filter);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/HookReceiver.java"
            line="287"
            column="13"/>
    </issue>

    <issue
        id="UnspecifiedRegisterReceiverFlag"
        message="`injectionReceiver` is missing `RECEIVER_EXPORTED` or `RECEIVER_NOT_EXPORTED` flag for unprotected broadcasts registered for com.example.ypb_xp.HOOK_INJECTION"
        errorLine1="            registerReceiver(injectionReceiver, filter);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/TargetAppHookReceiver.java"
            line="83"
            column="13"/>
    </issue>

    <issue
        id="UseSwitchCompatOrMaterialCode"
        message="Use `SwitchCompat` from AppCompat or `MaterialSwitch` from Material library"
        errorLine1="        Switch moduleSwitch;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/ui/XposedModuleAdapter.java"
            line="109"
            column="9"/>
    </issue>

    <issue
        id="UseSwitchCompatOrMaterialXml"
        message="Use `SwitchCompat` from AppCompat or `MaterialSwitch` from Material library"
        errorLine1="        &lt;Switch"
        errorLine2="        ^">
        <location
            file="src/main/res/layout/item_xposed_module.xml"
            line="108"
            column="9"/>
    </issue>

    <issue
        id="AndroidGradlePluginVersion"
        message="A newer version of com.android.application than 8.11.1 is available: 8.12.0"
        errorLine1="agp = &quot;8.11.1&quot;"
        errorLine2="      ~~~~~~~~">
        <location
            file="../gradle/libs.versions.toml"
            line="2"
            column="7"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.recyclerview:recyclerview than 1.3.2 is available: 1.4.0"
        errorLine1="    implementation(&quot;androidx.recyclerview:recyclerview:1.3.2&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="74"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.test.ext:junit than 1.2.1 is available: 1.3.0"
        errorLine1="junitVersion = &quot;1.2.1&quot;"
        errorLine2="               ~~~~~~~">
        <location
            file="../gradle/libs.versions.toml"
            line="4"
            column="16"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.test.espresso:espresso-core than 3.6.1 is available: 3.7.0"
        errorLine1="espressoCore = &quot;3.6.1&quot;"
        errorLine2="               ~~~~~~~">
        <location
            file="../gradle/libs.versions.toml"
            line="5"
            column="16"/>
    </issue>

    <issue
        id="NewerVersionAvailable"
        message="A newer version of com.google.code.gson:gson than 2.10.1 is available: 2.13.1"
        errorLine1="    implementation(&quot;com.google.code.gson:gson:2.10.1&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="68"
            column="20"/>
    </issue>

    <issue
        id="NewerVersionAvailable"
        message="A newer version of com.squareup.okhttp3:okhttp than 4.12.0 is available: 5.1.0"
        errorLine1="    implementation(&quot;com.squareup.okhttp3:okhttp:4.12.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="80"
            column="20"/>
    </issue>

    <issue
        id="NewerVersionAvailable"
        message="A newer version of io.reactivex.rxjava3:rxjava than 3.1.8 is available: 3.1.11"
        errorLine1="    implementation(&quot;io.reactivex.rxjava3:rxjava:3.1.8&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="83"
            column="20"/>
    </issue>

    <issue
        id="PrivateResource"
        message="The resource `@drawable/abc_ic_ab_back_material` is marked as private in com.google.android.material:material:1.12.0"
        errorLine1="            binding.toolbar.setNavigationIcon(androidx.appcompat.R.drawable.abc_ic_ab_back_material);"
        errorLine2="                                                                            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/XposedModulesActivity.java"
            line="45"
            column="77"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;🔧 开始修复Binder IPC连接问题...&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/AutoFixer.java"
            line="20"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;步骤1: 重启VirtualizationService&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/AutoFixer.java"
            line="24"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.i(TAG, &quot;✅ 服务重启成功&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/AutoFixer.java"
            line="26"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;步骤3: 测试Binder IPC连接&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/AutoFixer.java"
            line="32"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.i(TAG, &quot;✅ Binder IPC修复成功!&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/AutoFixer.java"
            line="34"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.w(TAG, &quot;⚠️ Binder IPC仍然失败，但系统会使用传统模式&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/AutoFixer.java"
            line="37"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;步骤4: 验证传统模式&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/AutoFixer.java"
            line="42"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;自动修复过程中出现异常&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/AutoFixer.java"
            line="46"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;服务重启命令已发送&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/AutoFixer.java"
            line="71"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;重启服务失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/AutoFixer.java"
            line="75"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                        Log.d(TAG, &quot;测试连接成功&quot;);"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/AutoFixer.java"
            line="92"
            column="25"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                        Log.d(TAG, &quot;测试连接断开&quot;);"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/AutoFixer.java"
            line="97"
            column="25"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                        Log.d(TAG, &quot;测试连接失败: &quot; + error);"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/AutoFixer.java"
            line="102"
            column="25"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;测试连接异常&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/AutoFixer.java"
            line="119"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;验证传统模式功能...&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/AutoFixer.java"
            line="129"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.e(TAG, &quot;引擎初始化失败&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/AutoFixer.java"
            line="136"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;当前运行模式: &quot; + currentMode);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/AutoFixer.java"
            line="143"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.i(TAG, &quot;✅ 传统模式功能验证成功&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/AutoFixer.java"
            line="149"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.w(TAG, &quot;⚠️ 虚拟环境创建失败，但引擎基本功能正常&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/AutoFixer.java"
            line="152"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.w(TAG, &quot;虚拟环境测试异常，但不影响基本功能&quot;, e);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/AutoFixer.java"
            line="156"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;验证传统模式失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/AutoFixer.java"
            line="161"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;🛠️ 开始一键自动修复...&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/AutoFixer.java"
            line="170"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;修复Binder IPC问题...&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/AutoFixer.java"
            line="177"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;重新初始化引擎...&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/AutoFixer.java"
            line="184"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;最终状态验证...&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/AutoFixer.java"
            line="189"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;✅ 自动修复完成&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/AutoFixer.java"
            line="192"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;🔄 重置虚拟化引擎...&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/AutoFixer.java"
            line="199"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.i(TAG, &quot;✅ 引擎重置成功&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/AutoFixer.java"
            line="212"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.i(TAG, &quot;当前模式: &quot; + engine.getCurrentMode());"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/AutoFixer.java"
            line="213"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;❌ 引擎重置失败&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/AutoFixer.java"
            line="215"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;引擎重置异常&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/AutoFixer.java"
            line="219"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;🔧 强制启用传统模式...&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/AutoFixer.java"
            line="227"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.i(TAG, &quot;✅ 传统模式强制启用成功&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/AutoFixer.java"
            line="240"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.i(TAG, &quot;当前模式: &quot; + engine.getCurrentMode());"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/AutoFixer.java"
            line="241"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;强制传统模式失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/AutoFixer.java"
            line="245"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;🎯 开始演示Binder IPC通信&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/BinderIPCDemo.java"
            line="31"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;📦 初始化虚拟化引擎...&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/BinderIPCDemo.java"
            line="56"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;✅ 引擎初始化成功 - 模式: &quot; + engine.getCurrentMode());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/BinderIPCDemo.java"
            line="61"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.i(TAG, &quot;🔗 Binder IPC模式已启用&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/BinderIPCDemo.java"
            line="65"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.w(TAG, &quot;📁 回退到传统模式&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/BinderIPCDemo.java"
            line="67"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;❌ 引擎初始化失败&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/BinderIPCDemo.java"
            line="70"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;🔌 创建独立IPC客户端...&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/BinderIPCDemo.java"
            line="78"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.i(TAG, &quot;✅ IPC客户端连接成功&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/BinderIPCDemo.java"
            line="84"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.d(TAG, &quot;服务状态: &quot; + status);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/BinderIPCDemo.java"
            line="88"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.w(TAG, &quot;⚠️ IPC客户端连接断开&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/BinderIPCDemo.java"
            line="93"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.e(TAG, &quot;❌ IPC客户端连接失败: &quot; + error);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/BinderIPCDemo.java"
            line="98"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;📱 开始克隆微信应用...&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/BinderIPCDemo.java"
            line="108"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;✅ 虚拟环境创建成功: &quot; + env.getCloneId());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/BinderIPCDemo.java"
            line="117"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.i(TAG, &quot;🚀 微信克隆启动成功&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/BinderIPCDemo.java"
            line="121"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;❌ 微信克隆启动失败&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/BinderIPCDemo.java"
            line="123"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.i(TAG, &quot;✅ IPC创建虚拟环境成功: &quot; + envInfo.getCloneId());"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/BinderIPCDemo.java"
            line="133"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;🎯 演示Hook注入...&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/BinderIPCDemo.java"
            line="142"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.i(TAG, &quot;✅ Hook注入成功: &quot; + result.getHookedMethodsCount() + &quot; 个方法&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/BinderIPCDemo.java"
            line="154"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.e(TAG, &quot;❌ Hook注入失败: &quot; + result.getErrorDetails());"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/BinderIPCDemo.java"
            line="156"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;🔧 管理虚拟环境...&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/BinderIPCDemo.java"
            line="166"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;环境信息: &quot; + envInfo.toString());"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/BinderIPCDemo.java"
            line="172"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;进程状态: &quot; + processStatus);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/BinderIPCDemo.java"
            line="177"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;文件重定向设置: &quot; + (redirectResult ? &quot;成功&quot; : &quot;失败&quot;));"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/BinderIPCDemo.java"
            line="183"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;🧹 清理资源...&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/BinderIPCDemo.java"
            line="191"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;✅ 资源清理完成&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/BinderIPCDemo.java"
            line="207"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;🔒 演示安全验证...&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/BinderIPCDemo.java"
            line="214"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;权限验证结果: &quot; + (hasPermission ? &quot;通过&quot; : &quot;失败&quot;));"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/BinderIPCDemo.java"
            line="221"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;⚡ 演示性能优势...&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/BinderIPCDemo.java"
            line="229"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;🔗 Binder IPC创建耗时: &quot; + (endTime - startTime) + &quot;ms&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/BinderIPCDemo.java"
            line="240"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.d(TAG, &quot;📁 传统模式创建耗时: &quot; + (endTime - startTime) + &quot;ms&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/BinderIPCDemo.java"
            line="249"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;开始创建APP克隆: &quot; + packageName);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="56"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;生成克隆ID: &quot; + cloneId);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="60"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;原始APP未安装: &quot; + packageName);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="64"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;原始APP已安装，继续创建克隆&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="67"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;创建虚拟环境...&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="70"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;虚拟环境创建失败&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="73"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;虚拟环境创建成功&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="76"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;复制APP数据...&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="79"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.w(TAG, &quot;APP数据复制可能失败，但继续进行&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="81"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;注册克隆...&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="86"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;保存配置...&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="91"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;APP克隆创建成功: &quot; + cloneId);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="94"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;创建APP克隆时出错: &quot; + packageName, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="98"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;开始启动克隆APP: &quot; + packageName + &quot;, CloneID: &quot; + cloneId);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="108"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;原始APP未安装，无法启动克隆: &quot; + packageName);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="112"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;虚拟环境不存在，重新创建: &quot; + cloneId);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="118"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.e(TAG, &quot;无法创建虚拟环境&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="122"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;虚拟环境重新创建成功&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="126"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;使用现有虚拟环境: &quot; + cloneId);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="128"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;获取主Activity...&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="132"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;无法获取主Activity: &quot; + packageName);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="135"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;主Activity: &quot; + mainActivity);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="138"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;应用Xposed模块...&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="141"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;启动虚拟化APP...&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="145"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.i(TAG, &quot;克隆APP启动成功: &quot; + packageName);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="150"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;克隆APP启动失败: &quot; + packageName);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="152"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;启动克隆APP时出错: &quot; + packageName + &quot;, CloneID: &quot; + cloneId, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="158"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;停止克隆APP时出错&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="179"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;开始删除APP克隆: &quot; + cloneId);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="189"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;虚拟环境清理结果: &quot; + cleanupResult);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="198"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.i(TAG, &quot;APP克隆删除成功: &quot; + cloneId);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="205"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.w(TAG, &quot;未找到要删除的克隆: &quot; + cloneId);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="207"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;删除APP克隆时出错: &quot; + cloneId, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="213"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;开始清除APP克隆缓存: &quot; + cloneId);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="223"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;找不到克隆环境: &quot; + cloneId);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="237"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;缓存清除结果: &quot; + result + &quot; for &quot; + cloneId);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="242"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;清除缓存时出错: &quot; + cloneId, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="246"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;开始清除APP克隆数据: &quot; + cloneId);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="256"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;找不到克隆环境: &quot; + cloneId);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="270"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;数据清除结果: &quot; + result + &quot; for &quot; + cloneId);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="275"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;清除数据时出错: &quot; + cloneId, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="279"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;重命名克隆APP: &quot; + cloneId + &quot; -> &quot; + newName);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="289"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.i(TAG, &quot;克隆APP重命名成功&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="295"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.w(TAG, &quot;未找到要重命名的克隆: &quot; + cloneId);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="300"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;重命名克隆APP时出错: &quot; + cloneId, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="304"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;开始复制APP数据: &quot; + originalDataPath + &quot; -> &quot; + env.getDataPath());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="324"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.w(TAG, &quot;无法访问原始数据目录，创建空的克隆环境: &quot; + originalDataPath);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="330"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;数据复制结果: &quot; + result);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="342"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;复制APP数据时出错: &quot; + packageName, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="346"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;找到主Activity: &quot; + mainActivity + &quot; for &quot; + packageName);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="375"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;使用第一个Activity作为主Activity: &quot; + mainActivity + &quot; for &quot; + packageName);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="383"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.w(TAG, &quot;无法找到主Activity for &quot; + packageName);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="387"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;获取主Activity时出错: &quot; + packageName, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="391"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;注册克隆时出错&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="422"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;加载克隆配置时出错&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="454"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;保存克隆配置时出错&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="479"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.w(TAG, &quot;原始APP不存在，跳过克隆: &quot; + config.packageName);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/CloneManager.java"
            line="505"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;Hook管理器已初始化，跳过&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="36"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;🚀 在目标进程中初始化Hook管理器&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="40"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.d(TAG, &quot;   - 目标包名: &quot; + packageName);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="41"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.d(TAG, &quot;   - 克隆ID: &quot; + cloneId);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="42"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.d(TAG, &quot;   - 目标进程ID: &quot; + android.os.Process.myPid());"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="43"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;✅ Activity Hook初始化成功&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="51"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;✅ 文件系统Hook初始化成功&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="55"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;✅ PackageManager Hook初始化成功&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="59"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;✅ 存储Hook初始化成功&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="63"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;🎉 Hook管理器初始化完成！目标应用现在运行在虚拟环境中&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="67"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;❌ Hook管理器初始化失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="72"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;初始化Activity Hook...&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="82"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.d(TAG, &quot;🎯 [&quot; + currentCloneId + &quot;] Activity.onCreate被调用: &quot; + param.thisObject.getClass().getName());"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="91"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.d(TAG, &quot;✅ [&quot; + currentCloneId + &quot;] Activity.onCreate完成: &quot; + param.thisObject.getClass().getName());"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="99"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.d(TAG, &quot;🎯 [&quot; + currentCloneId + &quot;] Activity.onResume: &quot; + param.thisObject.getClass().getName());"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="108"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;Activity Hook初始化失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="118"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;初始化文件系统Hook...&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="128"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                        Log.d(TAG, &quot;🔄 [&quot; + currentCloneId + &quot;] 文件路径重定向:&quot;);"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="141"
            column="25"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                        Log.d(TAG, &quot;   原路径: &quot; + originalPath);"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="142"
            column="25"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                        Log.d(TAG, &quot;   新路径: &quot; + redirectedPath);"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="143"
            column="25"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                        Log.d(TAG, &quot;🔄 [&quot; + currentCloneId + &quot;] 父目录路径重定向: &quot; + originalParent + &quot; -> &quot; + redirectedParent);"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="158"
            column="25"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;文件系统Hook初始化失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="167"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;初始化PackageManager Hook...&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="177"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                        Log.d(TAG, &quot;🔍 [&quot; + currentCloneId + &quot;] 已过滤宿主应用，返回 &quot; + packages.size() + &quot; 个包&quot;);"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="194"
            column="25"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;PackageManager Hook初始化失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="203"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;初始化存储Hook...&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="213"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.d(TAG, &quot;🔄 [&quot; + currentCloneId + &quot;] SharedPreferences重定向: &quot; + originalName + &quot; -> &quot; + redirectedName);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="226"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;存储Hook初始化失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="234"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;路径重定向失败: &quot; + originalPath, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="279"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;设置虚拟环境: &quot; + activity.getClass().getName());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="289"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;设置虚拟环境失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="295"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;验证虚拟环境状态: &quot; + currentCloneId);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="305"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;验证虚拟环境失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="308"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;=== Hook状态报告 ===&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="330"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;初始化状态: &quot; + isInitialized);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="331"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;当前克隆ID: &quot; + currentCloneId);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="332"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;宿主包名: &quot; + hostPackageName);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="333"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;当前进程ID: &quot; + android.os.Process.myPid());"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="334"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;==================&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hooks/CoreHookManager.java"
            line="335"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;正在初始化Hook框架...&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/HookFramework.java"
            line="19"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;LSPlant初始化失败&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/HookFramework.java"
            line="23"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;核心Hook设置失败&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/HookFramework.java"
            line="29"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;Hook框架初始化成功&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/HookFramework.java"
            line="34"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;Hook框架初始化异常&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/HookFramework.java"
            line="38"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;Hook框架未初始化&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/HookFramework.java"
            line="48"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;为虚拟环境应用Hook: &quot; + env.getCloneId());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/HookFramework.java"
            line="53"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;系统API Hook失败&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/HookFramework.java"
            line="57"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;文件操作Hook失败&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/HookFramework.java"
            line="63"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;网络操作Hook失败&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/HookFramework.java"
            line="69"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;IPC Hook失败&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/HookFramework.java"
            line="75"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;Hook应用成功&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/HookFramework.java"
            line="79"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;应用Hook时出错&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/HookFramework.java"
            line="83"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;设置核心Hook时出错&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/HookFramework.java"
            line="111"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;Hook系统API时出错&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/HookFramework.java"
            line="139"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;Hook文件操作时出错&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/HookFramework.java"
            line="153"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;Hook网络操作时出错&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/HookFramework.java"
            line="167"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;Hook IPC时出错&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/HookFramework.java"
            line="190"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;移除Hook时出错&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/HookFramework.java"
            line="202"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;🚀 初始化Hook注入器&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/HookInjector.java"
            line="35"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.i(TAG, &quot;✅ Hook注入器SO库加载成功&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/HookInjector.java"
            line="42"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;❌ 加载Hook注入器SO库失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/HookInjector.java"
            line="47"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;❌ 初始化Hook注入器失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/HookInjector.java"
            line="50"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;🎯 在目标进程中初始化Hook环境&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/HookInjector.java"
            line="59"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;❌ Hook注入器SO库未加载&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/HookInjector.java"
            line="62"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.i(TAG, &quot;✅ Hook环境初始化成功&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/HookInjector.java"
            line="69"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;❌ Hook环境初始化失败&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/HookInjector.java"
            line="71"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;❌ Hook环境初始化异常&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/HookInjector.java"
            line="75"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;🔄 在目标进程中加载Xposed模块: &quot; + modulePackageName);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/HookInjector.java"
            line="84"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.d(TAG, &quot;📂 模块路径: &quot; + modulePath);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/HookInjector.java"
            line="85"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;❌ Hook注入器SO库未加载&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/HookInjector.java"
            line="88"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;❌ 模块文件不存在: &quot; + modulePath);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/HookInjector.java"
            line="95"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.i(TAG, &quot;✅ Xposed模块加载成功: &quot; + modulePackageName);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/HookInjector.java"
            line="102"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;❌ Xposed模块加载失败: &quot; + modulePackageName);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/HookInjector.java"
            line="104"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;❌ 加载Xposed模块异常: &quot; + modulePackageName, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/HookInjector.java"
            line="108"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;🎯 在目标进程中执行Hook: &quot; + targetPackageName);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/HookInjector.java"
            line="117"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;❌ Hook注入器SO库未加载&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/HookInjector.java"
            line="120"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.i(TAG, &quot;✅ Hook执行成功: &quot; + targetPackageName);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/HookInjector.java"
            line="127"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;❌ Hook执行失败: &quot; + targetPackageName);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/HookInjector.java"
            line="129"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;❌ Hook执行异常: &quot; + targetPackageName, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/HookInjector.java"
            line="133"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;当前进程: &quot; + processName + &quot;, 在目标进程中: &quot; + inTarget);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/HookInjector.java"
            line="147"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;检查进程状态失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/HookInjector.java"
            line="150"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;获取进程名失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/HookInjector.java"
            line="167"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;Hook注入器服务启动&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/HookInjectorService.java"
            line="18"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.d(TAG, &quot;Hook注入器服务收到启动命令&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/HookInjectorService.java"
            line="23"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;Hook注入器服务销毁&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/HookInjectorService.java"
            line="46"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;执行Hook注入: &quot; + packageName + &quot;, CloneID: &quot; + cloneId);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/HookInjectorService.java"
            line="50"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;注册Hook: &quot; + method.getDeclaringClass().getSimpleName() + &quot;.&quot; + method.getName());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/HookInterceptor.java"
            line="27"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;处理静态方法Hook: &quot; + method.getName());"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/HookInterceptor.java"
            line="33"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;处理实例方法Hook: &quot; + method.getName());"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/HookInterceptor.java"
            line="36"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;注册Hook失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/HookInterceptor.java"
            line="41"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;🎯 静态方法Hook已注册: &quot; + method.getName());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/HookInterceptor.java"
            line="52"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;静态方法Hook失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/HookInterceptor.java"
            line="59"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;🎯 实例方法Hook已注册: &quot; + method.getName());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/HookInterceptor.java"
            line="69"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;实例方法Hook失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/HookInterceptor.java"
            line="72"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;🎯 拦截方法调用: &quot; + method.getName());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/HookInterceptor.java"
            line="83"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.i(TAG, &quot;✅ Hook拦截成功，返回自定义结果: &quot; + param.getResult());"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/HookInterceptor.java"
            line="97"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;Hook执行失败&quot;, t);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/HookInterceptor.java"
            line="114"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;🧪 模拟Hook调用: &quot; + method.getName());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/HookInterceptor.java"
            line="131"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.i(TAG, &quot;🎉 模拟Hook生效！返回值: &quot; + param.getResult());"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/HookInterceptor.java"
            line="144"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.d(TAG, &quot;Hook未设置返回值，将调用原方法&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/HookInterceptor.java"
            line="146"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.i(TAG, &quot;✅ 模拟Hook调用完成&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/HookInterceptor.java"
            line="152"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;模拟Hook调用失败&quot;, t);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/HookInterceptor.java"
            line="156"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;=== Hook拦截器状态 ===&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/HookInterceptor.java"
            line="187"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;  &quot; + method.getDeclaringClass().getSimpleName() + &quot;.&quot; + method.getName());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/HookInterceptor.java"
            line="190"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;总计: &quot; + methodHooks.size() + &quot; 个Hook&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/HookInterceptor.java"
            line="192"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;取消Hook: &quot; + method.getName());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/HookInterceptor.java"
            line="201"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;🎯 目标进程收到Hook注入指令&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/HookReceiver.java"
            line="24"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.d(TAG, &quot;   - 目标进程ID: &quot; + android.os.Process.myPid());"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/HookReceiver.java"
            line="25"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.d(TAG, &quot;   - Intent: &quot; + intent);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/HookReceiver.java"
            line="26"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;未知Action: &quot; + action);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/HookReceiver.java"
            line="34"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;处理Hook注入指令异常&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/HookReceiver.java"
            line="38"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;🚀 开始处理Hook注入...&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/HookReceiver.java"
            line="47"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;注入参数:&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/HookReceiver.java"
            line="54"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;   - 包名: &quot; + packageName);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/HookReceiver.java"
            line="55"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;   - 克隆ID: &quot; + cloneId);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/HookReceiver.java"
            line="56"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;   - 指令文件: &quot; + injectionFile);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/HookReceiver.java"
            line="57"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;❌ 注入参数不完整&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/HookReceiver.java"
            line="61"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.i(TAG, &quot;✅ Hook注入成功！目标应用现在运行在虚拟环境中&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/HookReceiver.java"
            line="78"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;❌ Hook注入失败&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/HookReceiver.java"
            line="84"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;处理Hook注入异常&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/HookReceiver.java"
            line="89"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.w(TAG, &quot;注入配置文件不存在: &quot; + filePath);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/HookReceiver.java"
            line="106"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;✅ 读取注入配置成功: &quot; + config.toString());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/HookReceiver.java"
            line="125"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;读取注入配置失败: &quot; + filePath, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/HookReceiver.java"
            line="130"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;创建默认注入配置: &quot; + config.toString());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/HookReceiver.java"
            line="146"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;创建默认配置失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/HookReceiver.java"
            line="150"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;⚡ 开始执行Hook注入...&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/HookReceiver.java"
            line="160"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;Hook管理器已初始化，跳过重复注入&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/HookReceiver.java"
            line="167"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.i(TAG, &quot;✅ Hook管理器初始化成功&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/HookReceiver.java"
            line="178"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.i(TAG, &quot;✅ Hook功能测试通过&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/HookReceiver.java"
            line="185"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.w(TAG, &quot;⚠️ Hook功能测试失败，但注入可能已成功&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/HookReceiver.java"
            line="188"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;❌ Hook管理器初始化失败&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/HookReceiver.java"
            line="193"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;执行Hook注入异常&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/HookReceiver.java"
            line="198"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;等待应用准备就绪...&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/HookReceiver.java"
            line="208"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;✅ 应用上下文已准备就绪&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/HookReceiver.java"
            line="215"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;等待应用就绪异常&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/HookReceiver.java"
            line="222"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;测试Hook功能...&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/HookReceiver.java"
            line="231"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;测试文件路径: &quot; + testFile.getAbsolutePath());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/HookReceiver.java"
            line="237"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;路径重定向测试: &quot; + (redirected ? &quot;成功&quot; : &quot;失败&quot;));"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/HookReceiver.java"
            line="243"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;测试Hook功能异常&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/HookReceiver.java"
            line="248"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;📡 发送注入结果: &quot; + (success ? &quot;成功&quot; : &quot;失败&quot;) + &quot; - &quot; + message);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/HookReceiver.java"
            line="268"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;发送注入结果失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/HookReceiver.java"
            line="271"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;🔗 在目标进程中注册Hook接收器&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/HookReceiver.java"
            line="280"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;✅ Hook接收器注册成功&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/HookReceiver.java"
            line="289"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;注册Hook接收器失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/HookReceiver.java"
            line="292"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;🧪 测试类加载: &quot; + className);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/debug/HookTestHelper.java"
            line="19"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;✅ 类加载成功: &quot; + className);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/debug/HookTestHelper.java"
            line="23"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;类详情: &quot; + clazz.toString());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/debug/HookTestHelper.java"
            line="24"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;方法数量: &quot; + clazz.getDeclaredMethods().length);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/debug/HookTestHelper.java"
            line="25"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;❌ 类加载失败: &quot; + className, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/debug/HookTestHelper.java"
            line="33"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;🔍 检查 &quot; + className + &quot; 类的方法:&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/debug/HookTestHelper.java"
            line="43"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;  方法: &quot; + method.getName() + &quot;(&quot; + "
        errorLine2="                ^">
        <location
            file="src/main/java/com/example/ypb_xp/debug/HookTestHelper.java"
            line="47"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.i(TAG, &quot;✅ 找到可能的Hook目标方法: &quot; + methodName);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/debug/HookTestHelper.java"
            line="60"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.d(TAG, &quot;   返回类型: &quot; + method.getReturnType());"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/debug/HookTestHelper.java"
            line="61"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.d(TAG, &quot;   参数类型: &quot; + java.util.Arrays.toString(method.getParameterTypes()));"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/debug/HookTestHelper.java"
            line="62"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.d(TAG, &quot;   修饰符: &quot; + java.lang.reflect.Modifier.toString(method.getModifiers()));"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/debug/HookTestHelper.java"
            line="63"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;测试类方法时出错: &quot; + className, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/debug/HookTestHelper.java"
            line="68"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;🎯 测试Hook &quot; + className + &quot;.&quot; + methodName);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/debug/HookTestHelper.java"
            line="77"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                            Log.i(TAG, &quot;🎉 Hook成功！&quot; + methodName + &quot; 被调用了&quot;);"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/debug/HookTestHelper.java"
            line="88"
            column="29"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                            Log.d(TAG, &quot;  this对象: &quot; + param.thisObject);"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/debug/HookTestHelper.java"
            line="89"
            column="29"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                            Log.d(TAG, &quot;  参数: &quot; + java.util.Arrays.toString(param.args));"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/debug/HookTestHelper.java"
            line="90"
            column="29"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                            Log.i(TAG, &quot;✅ &quot; + methodName + &quot; 返回结果: &quot; + param.getResult());"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/debug/HookTestHelper.java"
            line="104"
            column="29"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.i(TAG, &quot;✅ 动态Hook设置成功: &quot; + className + &quot;.&quot; + methodName);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/debug/HookTestHelper.java"
            line="110"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.i(TAG, &quot;当前已Hook方法数量: &quot; + hookCount);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/debug/HookTestHelper.java"
            line="114"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;❌ Hook设置失败: &quot; + className + &quot;.&quot; + methodName);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/debug/HookTestHelper.java"
            line="119"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;❌ Hook测试失败: &quot; + className + &quot;.&quot; + methodName, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/debug/HookTestHelper.java"
            line="124"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;🧪 开始测试应用相关类，类数量: &quot; + classNamesToTest.length);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/debug/HookTestHelper.java"
            line="133"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;✅ 应用类测试完成&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/debug/HookTestHelper.java"
            line="139"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;🧪 开始自动发现Hook目标（兼容模式）&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/debug/HookTestHelper.java"
            line="146"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;无法Hook isGlobalSpdySwitchOpen: &quot; + e.getMessage());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/debug/HookTestHelper.java"
            line="162"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;✅ 兼容模式测试完成&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/debug/HookTestHelper.java"
            line="165"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;🧪 开始自动发现Hook目标&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/debug/HookTestHelper.java"
            line="172"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;类不存在或无法加载: &quot; + pattern);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/debug/HookTestHelper.java"
            line="190"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;✅ 自动发现完成&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/debug/HookTestHelper.java"
            line="194"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;🎯 开始真正的Xposed模块注入&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/ProcessInjector.java"
            line="22"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.d(TAG, &quot;   - 目标应用: &quot; + packageName);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/ProcessInjector.java"
            line="23"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.d(TAG, &quot;   - 克隆ID: &quot; + cloneId);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/ProcessInjector.java"
            line="24"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.d(TAG, &quot;   - 目标PID: &quot; + targetPid);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/ProcessInjector.java"
            line="25"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;❌ 注入环境准备失败&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/ProcessInjector.java"
            line="30"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;❌ 无法获取目标APK路径&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/ProcessInjector.java"
            line="37"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.i(TAG, &quot;✅ Hook库注入成功&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/ProcessInjector.java"
            line="43"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.i(TAG, &quot;✅ Xposed框架在目标进程中初始化成功&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/ProcessInjector.java"
            line="47"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                        Log.i(TAG, &quot;✅ Hook模块加载成功&quot;);"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/ProcessInjector.java"
            line="51"
            column="25"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                            Log.i(TAG, &quot;🎉 Xposed模块完全加载成功！&quot;);"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/ProcessInjector.java"
            line="55"
            column="29"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;❌ 进程注入异常&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/ProcessInjector.java"
            line="63"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;📋 准备注入环境...&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/ProcessInjector.java"
            line="74"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;创建注入目录失败: &quot; + injectionDir.getAbsolutePath());"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/ProcessInjector.java"
            line="79"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;写入Hook配置失败&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/ProcessInjector.java"
            line="88"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;复制Native库失败&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/ProcessInjector.java"
            line="94"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;✅ 注入环境准备完成&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/ProcessInjector.java"
            line="98"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;准备注入环境异常&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/ProcessInjector.java"
            line="102"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;目标APK路径: &quot; + appInfo.sourceDir);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/ProcessInjector.java"
            line="115"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;获取目标APK路径失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/ProcessInjector.java"
            line="119"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.d(TAG, &quot;🚀 开始Hook库注入...&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/ProcessInjector.java"
            line="128"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.i(TAG, &quot;✅ Hook库成功注入到目标进程&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/ProcessInjector.java"
            line="135"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.i(TAG, &quot;✅ 注入验证成功&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/ProcessInjector.java"
            line="142"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.w(TAG, &quot;⚠️ 注入验证失败，但继续执行&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/ProcessInjector.java"
            line="145"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;❌ Hook库注入失败&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/ProcessInjector.java"
            line="150"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;Hook库注入异常&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/ProcessInjector.java"
            line="155"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.d(TAG, &quot;🔧 在目标进程中初始化Xposed框架...&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/ProcessInjector.java"
            line="164"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.i(TAG, &quot;✅ Xposed框架初始化成功&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/ProcessInjector.java"
            line="171"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;❌ Xposed框架初始化失败&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/ProcessInjector.java"
            line="178"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;Xposed框架初始化异常&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/ProcessInjector.java"
            line="183"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.d(TAG, &quot;📦 加载Hook模块...&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/ProcessInjector.java"
            line="192"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.d(TAG, &quot;✅ Hook模块加载成功: &quot; + module);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/ProcessInjector.java"
            line="207"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.w(TAG, &quot;⚠️ Hook模块加载失败: &quot; + module);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/ProcessInjector.java"
            line="209"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.i(TAG, &quot;✅ 所有Hook模块加载成功&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/ProcessInjector.java"
            line="215"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.w(TAG, &quot;⚠️ 部分Hook模块加载失败，但继续执行&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/ProcessInjector.java"
            line="217"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;加载Hook模块异常&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/ProcessInjector.java"
            line="223"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.d(TAG, &quot;⚡ 激活Hook...&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/ProcessInjector.java"
            line="232"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.i(TAG, &quot;✅ Hook激活成功&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/ProcessInjector.java"
            line="239"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.i(TAG, &quot;✅ Hook工作验证成功&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/ProcessInjector.java"
            line="243"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.w(TAG, &quot;⚠️ Hook验证失败，但可能已经工作&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/ProcessInjector.java"
            line="246"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;❌ Hook激活失败&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/ProcessInjector.java"
            line="251"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;激活Hook异常&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/ProcessInjector.java"
            line="256"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;写入文件失败: &quot; + file.getAbsolutePath(), e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/ProcessInjector.java"
            line="299"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;复制Native库失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/ProcessInjector.java"
            line="314"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;验证注入失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/ProcessInjector.java"
            line="327"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;验证Hook激活失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/injection/ProcessInjector.java"
            line="340"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;正在初始化进程隔离器...&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/ProcessIsolator.java"
            line="24"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;Native进程隔离器初始化失败&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/ProcessIsolator.java"
            line="28"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;进程隔离器初始化成功&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/ProcessIsolator.java"
            line="33"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;进程隔离器初始化异常&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/ProcessIsolator.java"
            line="37"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;进程隔离器未初始化&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/ProcessIsolator.java"
            line="47"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;为虚拟环境设置进程隔离: &quot; + env.getCloneId());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/ProcessIsolator.java"
            line="52"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;命名空间隔离设置失败&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/ProcessIsolator.java"
            line="56"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;内存隔离设置失败&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/ProcessIsolator.java"
            line="62"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;文件系统隔离设置失败&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/ProcessIsolator.java"
            line="68"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;网络隔离设置失败&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/ProcessIsolator.java"
            line="74"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;进程隔离设置成功&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/ProcessIsolator.java"
            line="78"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;设置进程隔离时出错&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/ProcessIsolator.java"
            line="82"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;设置命名空间隔离时出错&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/ProcessIsolator.java"
            line="95"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;设置内存隔离时出错&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/ProcessIsolator.java"
            line="108"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;设置文件系统隔离时出错&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/ProcessIsolator.java"
            line="125"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;设置网络隔离时出错&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/ProcessIsolator.java"
            line="138"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;创建隔离进程: &quot; + env.getPackageName());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/ProcessIsolator.java"
            line="152"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;创建隔离进程时出错&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/ProcessIsolator.java"
            line="162"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;终止隔离进程时出错&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/ProcessIsolator.java"
            line="174"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;🚀 开始一键克隆微信...&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/QuickStartDemo.java"
            line="21"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;❌ 引擎初始化失败&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/QuickStartDemo.java"
            line="28"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;❌ 虚拟环境创建失败&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/QuickStartDemo.java"
            line="38"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;✅ 微信克隆成功启动！&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/QuickStartDemo.java"
            line="45"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;📱 现在您可以同时使用两个微信账号了&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/QuickStartDemo.java"
            line="46"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;❌ 微信克隆启动失败&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/QuickStartDemo.java"
            line="48"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;🔧 演示高级IPC功能...&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/QuickStartDemo.java"
            line="56"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.i(TAG, &quot;✅ IPC连接成功&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/QuickStartDemo.java"
            line="65"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.w(TAG, &quot;⚠️ IPC连接断开&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/QuickStartDemo.java"
            line="75"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.e(TAG, &quot;❌ IPC连接失败: &quot; + error);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/QuickStartDemo.java"
            line="80"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.i(TAG, &quot;✅ &quot; + names[i] + &quot; 克隆创建成功&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/QuickStartDemo.java"
            line="102"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;📊 性能监控示例...&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/QuickStartDemo.java"
            line="114"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;服务状态: &quot; + status);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/QuickStartDemo.java"
            line="119"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;微信进程状态: &quot; + processStatus);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/QuickStartDemo.java"
            line="123"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;🔄 应用生命周期管理演示...&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/QuickStartDemo.java"
            line="131"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;📦 虚拟环境已创建&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/QuickStartDemo.java"
            line="143"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.i(TAG, &quot;🚀 应用已启动&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/QuickStartDemo.java"
            line="147"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.i(TAG, &quot;📊 监控应用运行状态...&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/QuickStartDemo.java"
            line="150"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.i(TAG, &quot;✅ 生命周期管理完成&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/QuickStartDemo.java"
            line="156"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;🛠️ 错误处理最佳实践...&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/QuickStartDemo.java"
            line="165"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;引擎初始化失败，检查权限和系统版本&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/QuickStartDemo.java"
            line="172"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.i(TAG, &quot;✅ 使用Binder IPC模式（推荐）&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/QuickStartDemo.java"
            line="178"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.w(TAG, &quot;⚠️ 回退到传统模式&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/QuickStartDemo.java"
            line="180"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;虚拟环境创建失败，可能的原因：&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/QuickStartDemo.java"
            line="186"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;1. 目标应用未安装&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/QuickStartDemo.java"
            line="187"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;2. 权限不足&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/QuickStartDemo.java"
            line="188"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;3. 存储空间不足&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/QuickStartDemo.java"
            line="189"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;✅ 错误处理演示完成&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/QuickStartDemo.java"
            line="193"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;未处理的异常&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/demo/QuickStartDemo.java"
            line="196"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;SecurityManager初始化完成 - DebugMode: &quot; + debugMode);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/security/SecurityManager.java"
            line="47"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.d(TAG, &quot;验证权限 - Package: &quot; + packageName + &quot;, UID: &quot; + callingUid + &quot;, PID: &quot; + callingPid);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/security/SecurityManager.java"
            line="54"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.w(TAG, &quot;无法获取调用者包名, UID: &quot; + callingUid);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/security/SecurityManager.java"
            line="60"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;调试模式 - 允许调用: &quot; + callerPackageName);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/security/SecurityManager.java"
            line="66"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;包名白名单验证通过: &quot; + callerPackageName);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/security/SecurityManager.java"
            line="72"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;系统应用验证通过: &quot; + callerPackageName);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/security/SecurityManager.java"
            line="78"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;签名验证通过: &quot; + callerPackageName);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/security/SecurityManager.java"
            line="84"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.w(TAG, &quot;权限验证失败: &quot; + callerPackageName);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/security/SecurityManager.java"
            line="88"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;权限验证异常&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/security/SecurityManager.java"
            line="92"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.d(TAG, &quot;签名匹配验证通过: &quot; + packageName);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/security/SecurityManager.java"
            line="111"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.w(TAG, &quot;签名不匹配: &quot; + packageName);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/security/SecurityManager.java"
            line="115"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;客户端权限验证异常&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/security/SecurityManager.java"
            line="122"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.d(TAG, &quot;授权包名列表: &quot; + authorizedPackages);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/security/SecurityManager.java"
            line="138"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.d(TAG, &quot;授权签名数量: &quot; + authorizedSignatures.size());"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/security/SecurityManager.java"
            line="151"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;获取包名失败, UID: &quot; + uid, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/security/SecurityManager.java"
            line="164"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.w(TAG, &quot;包不存在: &quot; + packageName);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/security/SecurityManager.java"
            line="177"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;签名验证失败: &quot; + packageName, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/security/SecurityManager.java"
            line="192"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;获取签名指纹失败: &quot; + packageName, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/security/SecurityManager.java"
            line="210"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;计算签名指纹失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/security/SecurityManager.java"
            line="235"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;检查调试构建失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/security/SecurityManager.java"
            line="249"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.d(TAG, &quot;添加授权包名: &quot; + packageName);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/security/SecurityManager.java"
            line="259"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.d(TAG, &quot;移除授权包名: &quot; + packageName);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/security/SecurityManager.java"
            line="267"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.d(TAG, &quot;添加授权签名: &quot; + signature);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/security/SecurityManager.java"
            line="275"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.d(TAG, &quot;系统应用权限设置: &quot; + allow);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/security/SecurityManager.java"
            line="283"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;🔍 开始系统诊断...&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/SystemDiagnostic.java"
            line="43"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;🔍 系统诊断完成 - 健康状态: &quot; + (result.isHealthy() ? &quot;良好&quot; : &quot;需要修复&quot;));"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/SystemDiagnostic.java"
            line="71"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                        Log.d(TAG, &quot;诊断：IPC连接成功&quot;);"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/SystemDiagnostic.java"
            line="218"
            column="25"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                        Log.d(TAG, &quot;诊断：IPC连接断开&quot;);"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/SystemDiagnostic.java"
            line="223"
            column="25"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                        Log.d(TAG, &quot;诊断：IPC连接失败 - &quot; + error);"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/SystemDiagnostic.java"
            line="228"
            column="25"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;🔍 快速系统检查...&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/SystemDiagnostic.java"
            line="306"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;引擎初始化状态: &quot; + engine.isInitialized());"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/SystemDiagnostic.java"
            line="310"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;当前通信模式: &quot; + engine.getCurrentMode());"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/SystemDiagnostic.java"
            line="311"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;Binder IPC可用: &quot; + engine.isBinderIPCEnabled());"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/SystemDiagnostic.java"
            line="312"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.i(TAG, &quot;✅ 引擎初始化成功&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/SystemDiagnostic.java"
            line="319"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.e(TAG, &quot;❌ 引擎初始化失败&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/SystemDiagnostic.java"
            line="321"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;快速检查异常&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/diagnostic/SystemDiagnostic.java"
            line="325"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.d(TAG, &quot;收到系统事件: &quot; + action);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/receiver/SystemEventReceiver.java"
            line="18"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;应用已安装: &quot; + packageName);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/receiver/SystemEventReceiver.java"
            line="31"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;应用已卸载: &quot; + packageName);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/receiver/SystemEventReceiver.java"
            line="38"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;处理Hook注入事件&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/receiver/SystemEventReceiver.java"
            line="44"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;🎯 在目标应用进程中启动Hook接收器&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/TargetAppHookReceiver.java"
            line="33"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;在主应用进程中，跳过Hook接收器初始化&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/TargetAppHookReceiver.java"
            line="36"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.i(TAG, &quot;✅ Hook注入器初始化成功&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/TargetAppHookReceiver.java"
            line="49"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.i(TAG, &quot;✅ Hook环境初始化成功&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/TargetAppHookReceiver.java"
            line="55"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.e(TAG, &quot;❌ Hook环境初始化失败&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/TargetAppHookReceiver.java"
            line="57"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;❌ Hook注入器初始化失败&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/TargetAppHookReceiver.java"
            line="60"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;🚀 Hook接收器启动完成&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/TargetAppHookReceiver.java"
            line="69"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;❌ Hook接收器初始化异常&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/TargetAppHookReceiver.java"
            line="72"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;📡 Hook注入广播接收器已注册&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/TargetAppHookReceiver.java"
            line="84"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;注册广播接收器失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/TargetAppHookReceiver.java"
            line="86"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;🔍 启动Hook指令监听器&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/TargetAppHookReceiver.java"
            line="96"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                        Log.i(TAG, &quot;📄 发现Hook注入指令文件: &quot; + commandFilePath);"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/TargetAppHookReceiver.java"
            line="107"
            column="25"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                            Log.d(TAG, &quot;🗑️ Hook指令文件已删除&quot;);"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/TargetAppHookReceiver.java"
            line="112"
            column="29"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.d(TAG, &quot;Hook指令监听器被中断&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/TargetAppHookReceiver.java"
            line="117"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.e(TAG, &quot;Hook指令监听异常&quot;, e);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/TargetAppHookReceiver.java"
            line="120"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;📋 处理Hook注入指令: &quot; + command);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/TargetAppHookReceiver.java"
            line="141"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;处理Hook指令异常&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/TargetAppHookReceiver.java"
            line="149"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;❌ Hook环境未初始化，无法执行注入&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/TargetAppHookReceiver.java"
            line="158"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;🎯 开始在目标进程中执行Hook注入&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/TargetAppHookReceiver.java"
            line="162"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.i(TAG, &quot;✅ Hook注入执行成功&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/TargetAppHookReceiver.java"
            line="170"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;❌ Hook注入执行失败&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/TargetAppHookReceiver.java"
            line="175"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;执行Hook注入异常&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/TargetAppHookReceiver.java"
            line="179"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;📦 尝试加载Xposed模块: &quot; + modulePackage);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/TargetAppHookReceiver.java"
            line="192"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;🎉 Xposed模块加载成功: &quot; + modulePackage);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/TargetAppHookReceiver.java"
            line="196"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;❌ Xposed模块加载失败: &quot; + modulePackage);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/TargetAppHookReceiver.java"
            line="198"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;📡 Hook注入广播接收器已注销&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/TargetAppHookReceiver.java"
            line="210"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;注销广播接收器失败&quot;, e);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/TargetAppHookReceiver.java"
            line="212"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;👋 Hook接收器已终止&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/TargetAppHookReceiver.java"
            line="216"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.i(TAG, &quot;📡 收到Hook注入触发广播&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/hookinjector/TargetAppHookReceiver.java"
            line="228"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;🔗 连接虚拟化服务...&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/client/VirtualizationClient.java"
            line="58"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;绑定服务失败&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/client/VirtualizationClient.java"
            line="67"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;连接超时&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/client/VirtualizationClient.java"
            line="78"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;✅ 虚拟化服务连接成功&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/client/VirtualizationClient.java"
            line="86"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;连接服务异常&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/client/VirtualizationClient.java"
            line="90"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;🔌 断开虚拟化服务连接&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/client/VirtualizationClient.java"
            line="102"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;断开连接异常&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/client/VirtualizationClient.java"
            line="115"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;创建虚拟环境: &quot; + packageName + &quot;, CloneID: &quot; + cloneId);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/client/VirtualizationClient.java"
            line="128"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;创建虚拟环境失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/client/VirtualizationClient.java"
            line="132"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;启动虚拟化应用: &quot; + packageName + &quot;, CloneID: &quot; + cloneId);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/client/VirtualizationClient.java"
            line="146"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;启动虚拟化应用失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/client/VirtualizationClient.java"
            line="150"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;注入Hook: &quot; + packageName + &quot;, CloneID: &quot; + cloneId);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/client/VirtualizationClient.java"
            line="164"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;Hook注入失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/client/VirtualizationClient.java"
            line="168"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;获取虚拟环境信息失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/client/VirtualizationClient.java"
            line="185"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;销毁虚拟环境: &quot; + cloneId);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/client/VirtualizationClient.java"
            line="199"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;销毁虚拟环境失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/client/VirtualizationClient.java"
            line="203"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;检查进程状态失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/client/VirtualizationClient.java"
            line="220"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;设置文件重定向失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/client/VirtualizationClient.java"
            line="237"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;获取服务状态失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/client/VirtualizationClient.java"
            line="254"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;验证权限失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/client/VirtualizationClient.java"
            line="271"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.w(TAG, &quot;服务未连接&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/client/VirtualizationClient.java"
            line="281"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;服务已连接: &quot; + name);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/client/VirtualizationClient.java"
            line="307"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.w(TAG, &quot;服务连接断开: &quot; + name);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/client/VirtualizationClient.java"
            line="323"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;🚀 正在初始化虚拟化引擎...&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="49"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.i(TAG, &quot;🔗 启用Binder IPC通信模式&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="53"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.d(TAG, &quot;服务启动请求已发送&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="57"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                                Log.i(TAG, &quot;✅ Binder IPC连接成功&quot;);"
        errorLine2="                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="64"
            column="33"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                                Log.w(TAG, &quot;⚠️ Binder IPC连接断开&quot;);"
        errorLine2="                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="69"
            column="33"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                                Log.e(TAG, &quot;❌ Binder IPC连接失败: &quot; + error);"
        errorLine2="                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="74"
            column="33"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                                Log.e(TAG, &quot;可能原因: 1.服务未启动 2.权限不足 3.进程隔离问题&quot;);"
        errorLine2="                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="75"
            column="33"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                        Log.w(TAG, &quot;⚠️ Binder IPC连接失败，回退到传统模式&quot;);"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="83"
            column="25"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                        Log.w(TAG, &quot;检查点: 1.AndroidManifest.xml配置 2.服务权限 3.进程状态&quot;);"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="84"
            column="25"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.e(TAG, &quot;❌ 无法启动VirtualizationService，回退到传统模式&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="88"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.i(TAG, &quot;📁 使用传统文件系统通信模式&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="95"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.e(TAG, &quot;Native组件初始化失败&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="99"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.e(TAG, &quot;Hook框架初始化失败&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="106"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.e(TAG, &quot;Hook注入器初始化失败&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="112"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.e(TAG, &quot;进程隔离器初始化失败&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="119"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;✅ XposedModuleLoader初始化成功&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="126"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;✅ 虚拟化引擎初始化成功 (模式: &quot; + (useBinderIPC ? &quot;Binder IPC&quot; : &quot;传统&quot;) + &quot;)&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="129"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;❌ 虚拟化引擎初始化异常&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="133"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;📦 为 &quot; + packageName + &quot; 创建虚拟环境, CloneID: &quot; + cloneId);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="147"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;🔗 使用Binder IPC创建虚拟环境&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="153"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.i(TAG, &quot;✅ Binder IPC虚拟环境创建成功: &quot; + cloneId);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="158"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.e(TAG, &quot;❌ Binder IPC虚拟环境创建失败&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="160"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;📁 使用传统模式创建虚拟环境&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="166"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.e(TAG, &quot;文件系统重定向设置失败&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="172"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.e(TAG, &quot;进程隔离设置失败&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="178"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.e(TAG, &quot;Hook应用失败&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="184"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.i(TAG, &quot;✅ 传统虚拟环境创建成功: &quot; + cloneId);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="188"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;❌ 创建虚拟环境时出错&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="194"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;虚拟化引擎未初始化&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="204"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;🚀 启动虚拟化APP: &quot; + env.getPackageName() + &quot;, CloneID: &quot; + env.getCloneId());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="209"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;🔗 使用Binder IPC启动虚拟化APP&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="215"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.i(TAG, &quot;✅ Binder IPC启动成功: &quot; + env.getPackageName());"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="219"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.e(TAG, &quot;❌ Binder IPC启动失败&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="221"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;📁 使用传统模式启动虚拟化APP&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="227"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.e(TAG, &quot;无法获取启动Intent: &quot; + env.getPackageName());"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="237"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;启动Intent: &quot; + launchIntent.toString());"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="250"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;目标Activity: &quot; + (launchIntent.getComponent() != null ? launchIntent.getComponent().getClassName() : &quot;null&quot;));"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="251"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.i(TAG, &quot;✅ 传统模式启动成功: &quot; + env.getPackageName());"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="257"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;❌ 启动虚拟化APP时出错: &quot; + env.getPackageName(), e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="271"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;设置文件系统重定向时出错&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="291"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;🚀 启动Hook注入任务: &quot; + packageName);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="335"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;⏳ 等待目标应用启动: &quot; + packageName);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="341"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                        Log.d(TAG, &quot;第 &quot; + retryCount + &quot;/&quot; + maxRetries + &quot; 次检查，目标进程未发现&quot;);"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="353"
            column="25"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.i(TAG, &quot;✅ 目标进程已启动，开始注入Hook&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="358"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.e(TAG, &quot;❌ 目标进程启动超时，尝试立即Hook策略&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="363"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.i(TAG, &quot;🚀 使用立即Hook策略处理: &quot; + packageName);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="367"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;Hook注入任务被中断&quot;, e);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="374"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;Hook注入任务异常&quot;, e);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="376"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;检查目标进程状态: &quot; + packageName + &quot;, PID: &quot; + pid + &quot;, 运行中: &quot; + isRunning);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="389"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;检查目标进程状态失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="392"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;🎯 开始向目标进程注入Hook: &quot; + packageName);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="402"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;❌ 无法获取目标进程PID&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="410"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;✅ 目标进程PID: &quot; + targetPid);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="413"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;🚀 尝试真正的进程注入...&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="416"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.i(TAG, &quot;🎉 真正的进程注入成功！&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="418"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.w(TAG, &quot;⚠️ 真正的进程注入失败，尝试其他方案&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="421"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;🔗 使用Binder IPC注入Hook&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="427"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                        Log.i(TAG, &quot;✅ Binder IPC Hook注入成功: &quot; + result.getHookedMethodsCount() + &quot; 个方法被Hook&quot;);"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="436"
            column="25"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                        Log.e(TAG, &quot;❌ Binder IPC Hook注入失败: &quot; + error);"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="440"
            column="25"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.e(TAG, &quot;❌ Hook载荷准备失败&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="443"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;📁 使用改进的文件系统通信注入Hook&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="449"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.i(TAG, &quot;✅ Hook注入流程完成: &quot; + packageName);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="457"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;❌ 所有Hook注入方式都失败: &quot; + packageName);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="466"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;❌ Hook注入过程异常&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="470"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;写入注入指令异常&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="493"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;🚀 触发目标进程执行Hook注入: &quot; + packageName);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="502"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;📡 已发送Hook注入触发广播&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="512"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;触发注入执行失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="515"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;准备Hook载荷失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="538"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;当前运行进程数量: &quot; + processes.size());"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="565"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.v(TAG, &quot;进程: &quot; + process.processName + &quot;, PID: &quot; + process.pid);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="567"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                        Log.d(TAG, &quot;找到目标进程: &quot; + packageName + &quot; -> &quot; + process.processName + &quot;, PID: &quot; + process.pid);"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="587"
            column="25"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                        Log.d(TAG, &quot;通过服务找到目标进程: &quot; + packageName + &quot;, PID: &quot; + service.pid);"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="598"
            column="25"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;通过服务查找进程失败&quot;, e);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="603"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                                Log.d(TAG, &quot;通过ps命令找到目标进程: &quot; + packageName + &quot;, PID: &quot; + pid);"
        errorLine2="                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="618"
            column="33"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;通过ps命令查找进程失败&quot;, e);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="626"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;未找到运行中的目标进程: &quot; + packageName);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="629"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;获取目标进程PID失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="633"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;🚀 立即Hook注入: &quot; + packageName);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="643"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;通过ModuleLoader直接注入Hook&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="647"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.d(TAG, &quot;Hook标志文件创建成功: &quot; + hookFlagPath);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="659"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.w(TAG, &quot;创建Hook标志文件失败&quot;, e);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="662"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;✅ 立即Hook注入完成: &quot; + packageName);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="672"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;立即Hook注入失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="675"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;🔍 验证Hook注入结果...&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="684"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;📡 已发送Hook验证测试广播&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="695"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;验证Hook注入异常&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="698"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.w(TAG, &quot;🔄 切换到传统模式&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="713"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;🚀 尝试启动VirtualizationService...&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="732"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;✅ VirtualizationService启动请求已发送&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="742"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;❌ 启动VirtualizationService失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="746"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;🎯 使用改进方案注入Hook: &quot; + packageName);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="755"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.i(TAG, &quot;✅ Hook指令写入成功: &quot; + injectionFile);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="765"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;❌ Hook指令写入失败，尝试备用方案&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="771"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;❌ 改进Hook注入方案异常&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="778"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.d(TAG, &quot;📁 尝试共享存储Hook注入方案&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="786"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.i(TAG, &quot;✅ 共享存储Hook指令写入成功: &quot; + injectionFile);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="796"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;❌ 共享存储方案也失败&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="802"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;❌ 共享存储Hook注入异常&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="806"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;📡 Hook注入广播已发送: &quot; + packageName);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="828"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;发送Hook注入广播失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="831"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;🧹 清理虚拟化引擎资源&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="839"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;✅ 资源清理完成&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="851"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;使用主应用已加载的Native库&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="44"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.w(TAG, &quot;Native库初始化异常&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="46"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;🚀 VirtualizationService启动中...&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="53"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;✅ 安全管理器初始化完成&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="58"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;✅ 线程池创建完成&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="62"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;✅ 组件初始化完成 (Java模式)&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="66"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;✅ VirtualizationService启动完成 - 进程ID: &quot; + Process.myPid());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="68"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;❌ VirtualizationService启动失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="71"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;🛑 VirtualizationService正在关闭...&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="79"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;✅ VirtualizationService已关闭&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="91"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;📱 客户端正在绑定服务&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="96"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.d(TAG, &quot;   - Intent: &quot; + intent);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="97"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.d(TAG, &quot;   - Caller UID: &quot; + Binder.getCallingUid());"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="98"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.d(TAG, &quot;   - Service PID: &quot; + Process.myPid());"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="99"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;✅ 返回Binder接口&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="102"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;❌ onBind异常&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="105"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;🔄 onStartCommand - startId: &quot; + startId);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="112"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.w(TAG, &quot;权限验证失败: &quot; + packageName + &quot;, Caller: &quot; + Binder.getCallingUid());"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="125"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;📦 创建虚拟环境: &quot; + packageName + &quot;, CloneID: &quot; + cloneId);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="129"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.w(TAG, &quot;虚拟环境已存在: &quot; + cloneId);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="134"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                                Log.i(TAG, &quot;✅ 虚拟环境创建成功: &quot; + cloneId);"
        errorLine2="                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="153"
            column="33"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                                Log.e(TAG, &quot;❌ 文件重定向设置失败: &quot; + cloneId);"
        errorLine2="                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="156"
            column="33"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                            Log.e(TAG, &quot;❌ 数据目录创建失败: &quot; + cloneId);"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="160"
            column="29"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                        Log.e(TAG, &quot;虚拟环境创建异常: &quot; + cloneId, e);"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="164"
            column="25"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;创建虚拟环境失败: &quot; + packageName, e);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="174"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;🚀 启动虚拟化应用: &quot; + packageName + &quot;, CloneID: &quot; + cloneId);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="186"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.e(TAG, &quot;虚拟环境不存在: &quot; + cloneId);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="191"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                            Log.i(TAG, &quot;✅ 虚拟化应用启动成功: &quot; + packageName);"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="215"
            column="29"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                        Log.e(TAG, &quot;启动虚拟化应用失败: &quot; + packageName, e);"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="219"
            column="25"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;启动虚拟化应用异常: &quot; + packageName, e);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="227"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;🎯 注入Hook: &quot; + packageName + &quot;, CloneID: &quot; + cloneId);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="239"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;Hook注入异常: &quot; + packageName, e);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="263"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;🗑️ 销毁虚拟环境: &quot; + cloneId);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="275"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;设置文件重定向: &quot; + packageName + &quot;, Rules: &quot; + redirectRules);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="291"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;设置文件重定向失败&quot;, e);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="302"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;获取服务状态失败&quot;, e);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="319"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.d(TAG, &quot;权限验证 - Package: &quot; + packageName + &quot;, UID: &quot; + callingUid + &quot;, PID: &quot; + callingPid);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="337"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;✅ 同应用调用，权限通过&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="341"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                        Log.d(TAG, &quot;✅ 同包名不同进程，权限通过: &quot; + pkg);"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="351"
            column="25"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.w(TAG, &quot;权限检查异常，默认允许&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="357"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.w(TAG, &quot;⚠️ 外部调用，暂时允许: &quot; + packageName);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="362"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.i(TAG, &quot;✅ 虚拟环境已销毁: &quot; + cloneId);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="387"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;销毁虚拟环境失败: &quot; + cloneId, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="394"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;初始化Java版本组件&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="413"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;Java组件初始化失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="416"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;创建目录: &quot; + path + &quot; -> &quot; + result);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="428"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;创建目录失败: &quot; + path, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="431"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;设置文件重定向: &quot; + packageName + &quot; -> &quot; + redirectPath);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="441"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;设置文件重定向失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="456"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;设置高级文件重定向: &quot; + packageName);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="466"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;设置高级文件重定向失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="474"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;准备启动应用: &quot; + packageName + &quot;, CloneID: &quot; + cloneId);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="484"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;应用启动准备失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="491"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;执行Hook注入: &quot; + packageName + &quot;, PID: &quot; + processId);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="500"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;Hook注入失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="505"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                        Log.d(TAG, &quot;找到进程: &quot; + packageName + &quot;, PID: &quot; + process.pid);"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="523"
            column="25"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;未找到运行中的进程: &quot; + packageName);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="529"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;获取进程ID失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="532"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;停止进程: &quot; + processId);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="542"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;停止进程失败: &quot; + processId, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="548"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;清理目录失败: &quot; + path, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="561"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;🎯 开始Hook方法: &quot; + method.getDeclaringClass().getName() + &quot;.&quot; + method.getName());"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedBridge.java"
            line="23"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;🎯 标准API Hook方法: &quot; + method.toString());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedBridge.java"
            line="24"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;直接进行Hook实现: &quot; + method.getName());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedBridge.java"
            line="31"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.i(TAG, &quot;✅ 标准API Hook模拟成功: &quot; + methodObj.getName());"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedBridge.java"
            line="34"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.w(TAG, &quot;Native Hook失败，使用备用方案&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedBridge.java"
            line="39"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                        Log.d(TAG, &quot;取消Hook: &quot; + methodObj.getName());"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedBridge.java"
            line="40"
            column="25"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.w(TAG, &quot;不支持的Member类型: &quot; + method.getClass().getName());"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedBridge.java"
            line="45"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.d(TAG, &quot;取消Hook: &quot; + method.getName());"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedBridge.java"
            line="47"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;Hook方法失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedBridge.java"
            line="50"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;✅ Hook成功: &quot; + method.getDeclaringClass().getSimpleName() + &quot;.&quot; + method.getName());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedBridge.java"
            line="54"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;Hook方法时出现异常: &quot; + method.getName(), e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedBridge.java"
            line="56"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;处理Hook方法调用: &quot; + method.getName());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedBridge.java"
            line="61"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;🧪 测试Hook效果: &quot; + method.getName());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedBridge.java"
            line="67"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.w(TAG, &quot;测试Hook时出错&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedBridge.java"
            line="70"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;Hook回调处理失败&quot;, t);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedBridge.java"
            line="85"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;调用原方法时出错&quot;, e);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedBridge.java"
            line="95"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, message);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedBridge.java"
            line="102"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.e(TAG, &quot;Xposed日志&quot;, t);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedBridge.java"
            line="106"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;Before hook回调时出错&quot;, t);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedBridge.java"
            line="111"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.e(TAG, &quot;调用原方法时出错&quot;, e);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedBridge.java"
            line="121"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;After hook回调时出错&quot;, t);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedBridge.java"
            line="131"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;Hook回调处理时出错&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedBridge.java"
            line="138"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;查找并Hook方法时出错: &quot; + clazz.getName() + &quot;.&quot; + methodName, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedBridge.java"
            line="166"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;Hook构造函数: &quot; + clazz.getName());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedBridge.java"
            line="190"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;查找并Hook构造函数时出错: &quot; + clazz.getName(), e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedBridge.java"
            line="202"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, message);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedBridge.java"
            line="297"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, message, t);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedBridge.java"
            line="301"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;尝试加载类: &quot; + className);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedBridge.java"
            line="316"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;使用ClassLoader: &quot; + classLoader.getClass().getName());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedBridge.java"
            line="317"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;✅ 成功加载类: &quot; + className);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedBridge.java"
            line="320"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;类信息: &quot; + clazz.getName());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedBridge.java"
            line="323"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;类加载器: &quot; + clazz.getClassLoader().getClass().getName());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedBridge.java"
            line="324"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;方法数量: &quot; + clazz.getDeclaredMethods().length);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedBridge.java"
            line="325"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.v(TAG, &quot;  方法: &quot; + method.getName());"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedBridge.java"
            line="329"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;❌ 无法找到类: &quot; + className, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedBridge.java"
            line="335"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.d(TAG, &quot;尝试使用系统ClassLoader加载: &quot; + className);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedBridge.java"
            line="341"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.i(TAG, &quot;✅ 使用系统ClassLoader成功加载: &quot; + className);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedBridge.java"
            line="343"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;系统ClassLoader也无法加载: &quot; + className);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedBridge.java"
            line="347"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;🎯 尝试Hook方法: &quot; + className + &quot;.&quot; + methodName);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedBridge.java"
            line="360"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;✅ 找到方法: &quot; + method.toString());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedBridge.java"
            line="377"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;❌ Hook方法失败: &quot; + className + &quot;.&quot; + methodName, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedBridge.java"
            line="383"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;✅ 找到类: &quot; + className);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="36"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;❌ 找不到类: &quot; + className, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="39"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;🎯 独立Hook方法: &quot; + clazz.getName() + &quot;.&quot; + methodName);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="49"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;参数数量: &quot; + parameterTypesAndCallback.length);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="50"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;最后一个参数类型: &quot; + lastParam.getClass().getName());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="64"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                        Log.d(TAG, &quot;参数类型[&quot; + i + &quot;]: &quot; + parameterTypes[i].getName());"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="74"
            column="25"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                        Log.e(TAG, &quot;参数[&quot; + i + &quot;]不是Class类型: &quot; + params[i].getClass().getName());"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="76"
            column="25"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;解析出方法参数类型数量: &quot; + parameterTypes.length);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="81"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;开始查找方法...&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="87"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;方法查找成功: &quot; + method.toString());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="89"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;Hook信息已存储&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="93"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;开始注册Hook拦截器...&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="96"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.i(TAG, &quot;✅ Hook拦截器注册成功: &quot; + clazz.getSimpleName() + &quot;.&quot; + methodName);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="99"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;开始模拟Hook调用...&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="102"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.d(TAG, &quot;模拟Hook调用完成&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="106"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.w(TAG, &quot;模拟Hook调用失败，但Hook已注册: &quot; + e.getMessage());"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="108"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.w(TAG, &quot;⚠️ Hook拦截器注册失败，但Hook信息已保存&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="111"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;创建Unhook对象...&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="115"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.d(TAG, &quot;取消Hook: &quot; + method.getName());"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="119"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;✅ Hook方法完成: &quot; + clazz.getSimpleName() + &quot;.&quot; + methodName);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="125"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;❌ Hook方法失败: &quot; + clazz.getName() + &quot;.&quot; + methodName, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="129"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;错误类型: &quot; + e.getClass().getName());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="130"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;错误消息: &quot; + e.getMessage());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="131"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;根本原因: &quot; + e.getCause().getClass().getName() + &quot;: &quot; + e.getCause().getMessage());"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="133"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.w(TAG, &quot;返回虚拟Unhook对象，避免中断模块&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="138"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.d(TAG, &quot;虚拟Unhook，无操作&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="142"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;🎯 Hook无参数方法: &quot; + clazz.getName() + &quot;.&quot; + methodName);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="153"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;方法查找成功: &quot; + method.toString());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="157"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;Hook信息已存储&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="161"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;开始注册Hook拦截器...&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="164"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.i(TAG, &quot;✅ Hook拦截器注册成功: &quot; + clazz.getSimpleName() + &quot;.&quot; + methodName);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="167"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;开始模拟Hook调用...&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="170"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.d(TAG, &quot;模拟Hook调用完成&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="174"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.w(TAG, &quot;模拟Hook调用失败，但Hook已注册: &quot; + e.getMessage());"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="176"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.w(TAG, &quot;⚠️ Hook拦截器注册失败，但Hook信息已保存&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="179"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.d(TAG, &quot;取消Hook: &quot; + method.getName());"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="186"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;✅ Hook方法完成: &quot; + clazz.getSimpleName() + &quot;.&quot; + methodName);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="192"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;❌ Hook无参数方法失败: &quot; + clazz.getName() + &quot;.&quot; + methodName, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="196"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.d(TAG, &quot;虚拟Unhook，无操作&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="203"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;🎯 独立Hook构造函数: &quot; + clazz.getName());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="222"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;✅ 独立Hook构造函数成功: &quot; + clazz.getSimpleName());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="243"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.d(TAG, &quot;取消Hook构造函数: &quot; + clazz.getName());"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="249"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;❌ Hook构造函数失败: &quot; + clazz.getName(), e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="254"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;✅ 找到方法: &quot; + method.toString());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="274"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;❌ 找不到方法: &quot; + clazz.getName() + &quot;.&quot; + methodName, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="277"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                        Log.d(TAG, &quot;✅ 找到兼容方法: &quot; + method.toString());"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="307"
            column="25"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;获取字段值失败: &quot; + fieldName, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="325"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;设置字段值失败: &quot; + fieldName, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="338"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;获取静态字段值失败: &quot; + fieldName, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="351"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;设置静态字段值失败: &quot; + fieldName, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="364"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;调用方法失败: &quot; + methodName, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="382"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;调用静态方法失败: &quot; + methodName, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="400"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;创建实例: &quot; + clazz.getName());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="410"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;✅ 实例创建成功: &quot; + clazz.getSimpleName());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="414"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;❌ 创建实例失败: &quot; + clazz.getName(), e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="417"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.d(TAG, &quot;创建实例: &quot; + clazz.getName() + &quot;，参数数量: &quot; + args.length);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="426"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;参数[&quot; + i + &quot;]类型: &quot; + paramTypes[i].getName() + &quot; 值: &quot; + args[i]);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="437"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;🔧 调用构造函数: &quot; + constructor);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="446"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;✅ 实例创建成功: &quot; + clazz.getSimpleName());"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="448"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;❌ 构造函数内部异常: &quot; + constructor);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="452"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;异常详情: &quot; + (cause != null ? cause.toString() : e.toString()));"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="453"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.e(TAG, &quot;根本原因: &quot; + cause.getMessage());"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="455"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;❌ 构造函数调用失败: &quot; + constructor, e);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="459"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.w(TAG, &quot;⚠️ 有参构造函数失败，尝试替代方案&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="464"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.d(TAG, &quot;🔄 尝试无参构造函数&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="475"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.w(TAG, &quot;无参构造函数失败: &quot; + fallbackException.getMessage());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="479"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.d(TAG, &quot;🔄 尝试Unsafe方式创建实例&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="483"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;✅ Unsafe方式创建成功&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="487"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.w(TAG, &quot;Unsafe创建失败: &quot; + e.getMessage());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="491"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.e(TAG, &quot;❌ 所有创建方式都失败&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="495"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.w(TAG, &quot;未找到Map参数的构造函数&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="513"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;🔄 尝试Map类型[&quot; + i + &quot;]: &quot; + testMaps[i].getClass().getSimpleName());"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="530"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;✅ Map类型[&quot; + i + &quot;]创建成功: &quot; + testMaps[i].getClass().getSimpleName());"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="533"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.w(TAG, &quot;Map类型[&quot; + i + &quot;]失败: &quot; + (cause != null ? cause.getMessage() : e.getMessage()));"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="537"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.w(TAG, &quot;Map类型[&quot; + i + &quot;]异常: &quot; + e.getMessage());"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="539"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.w(TAG, &quot;所有Map类型都失败&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="543"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;✅ Unsafe分配实例成功: &quot; + clazz.getSimpleName());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="610"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;Unsafe创建失败: &quot; + e.getMessage());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="613"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;🔧 ===== 解决建议 =====&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="622"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;📝 针对tb.jlj类的特殊建议:&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="625"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;  1. tb.jlj构造函数在第13行出错，可能需要特定的Map内容&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="626"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;  2. 尝试创建一个包含必要字段的Map:&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="627"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;     Map&lt;String, Object> config = new HashMap&lt;>();&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="628"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;     config.put(\&quot;url\&quot;, \&quot;https://api.example.com\&quot;);&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="629"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;     config.put(\&quot;timeout\&quot;, 5000);&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="630"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;     config.put(\&quot;retries\&quot;, 3);&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="631"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;  3. 或者检查tb.jlj的源码，看第13行期望什么参数&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="632"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;  4. 考虑使用其他类来替代tb.jlj的功能&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="633"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;🛠️ 通用解决方案:&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="636"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;  1. 检查原始代码，确认正确的实例化方式&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="637"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;  2. 使用反射查看类的字段和方法:&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="638"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;     Field[] fields = clazz.getDeclaredFields();&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="639"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;     Method[] methods = clazz.getDeclaredMethods();&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="640"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;  3. 尝试使用Builder模式或工厂方法&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="641"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;  4. 考虑使用代理类或适配器模式&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="642"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;💡 代码示例:&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="644"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;  // 方案1: 使用空Map&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="645"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;  Map&lt;String, Object> emptyMap = new HashMap&lt;>();&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="646"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;  Object obj = XposedHelpers.newInstance(clazz, emptyMap);&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="647"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="648"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;  // 方案2: 使用Unsafe（已自动尝试）&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="649"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;  // Object obj = createInstanceUnsafe(clazz);&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="650"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="651"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;  // 方案3: 查找静态工厂方法&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="652"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;  // Method createMethod = clazz.getMethod(\&quot;create\&quot;, Map.class);&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="653"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;  // Object obj = createMethod.invoke(null, mapParam);&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="654"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;================================&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="656"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;✅ 找到精确匹配构造函数: &quot; + constructor);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="668"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;✅ 找到兼容构造函数: &quot; + constructor);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="676"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;✅ 找到同参数数量构造函数: &quot; + constructor);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="684"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.w(TAG, &quot;❌ 未找到合适的构造函数&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="689"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;📋 &quot; + clazz.getName() + &quot; 可用构造函数:&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="730"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.i(TAG, &quot;  (无可用构造函数)&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="734"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.i(TAG, &quot;  [&quot; + i + &quot;] &quot; + modifiers + &quot; &quot; + clazz.getSimpleName() + &quot;(&quot; + params + &quot;)&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="749"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;💡 建议: 使用上述构造函数之一，或检查参数类型是否正确&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="752"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;🔍 类信息:&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="755"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;  - 类型: &quot; + clazz.getName());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="756"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;  - 包: &quot; + clazz.getPackage());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="757"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;  - 修饰符: &quot; + java.lang.reflect.Modifier.toString(clazz.getModifiers()));"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="758"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;  - 父类: &quot; + (clazz.getSuperclass() != null ? clazz.getSuperclass().getName() : &quot;无&quot;));"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="759"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;❌ 列出构造函数失败&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="762"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;🎯 处理Hook方法调用: &quot; + method.getName());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="772"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.d(TAG, &quot;✅ Hook拦截，返回自定义结果: &quot; + param.getResult());"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="786"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;Hook回调处理失败&quot;, t);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="803"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;=== 已Hook方法列表 ===&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="881"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;  &quot; + method.getDeclaringClass().getSimpleName() + &quot;.&quot; + method.getName());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="884"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;XposedHelpers总计: &quot; + hookedMethods.size() + &quot; 个方法&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="886"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;Hook拦截器总计: &quot; + HookInterceptor.getHookCount() + &quot; 个方法&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/de/robv/android/xposed_backup/XposedHelpers.java"
            line="889"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;开始扫描Xposed模块&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="68"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.d(TAG, &quot;发现Xposed模块: &quot; + moduleInfo.packageName + &quot; (启用: &quot; + moduleInfo.isEnabled + &quot;)&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="81"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;扫描完成，发现 &quot; + availableModules.size() + &quot; 个Xposed模块&quot;);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="86"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.w(TAG, &quot;检查xposed_init文件时出错: &quot; + apkPath, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="124"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;模块 &quot; + moduleInfo.packageName + &quot; 入口类数量: &quot; + moduleInfo.entryClasses.size());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="152"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;  入口类: &quot; + entryClass);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="154"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;创建模块信息时出错: &quot; + packageInfo.packageName, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="160"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;读取xposed_init文件时出错: &quot; + apkPath, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="188"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;读取xposed_init文件时出错: &quot; + apkPath, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="217"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;加载Xposed模块: &quot; + moduleInfo.packageName + &quot; for clone: &quot; + cloneId);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="228"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;准备加载 &quot; + moduleInfo.entryClasses.size() + &quot; 个入口类&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="244"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.w(TAG, &quot;模块没有入口类: &quot; + moduleInfo.packageName);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="246"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;正在加载入口类: &quot; + entryClass);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="251"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.d(TAG, &quot;✅ 成功加载入口类: &quot; + entryClass);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="253"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.w(TAG, &quot;❌ 加载入口类失败: &quot; + entryClass);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="255"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;模块加载完成: &quot; + moduleInfo.packageName);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="264"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;加载模块时出错: &quot; + moduleInfo.packageName, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="268"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;成功实例化模块入口类: &quot; + className);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="281"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.i(TAG, &quot;正在调用initZygote方法: &quot; + className);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="294"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.i(TAG, &quot;initZygote方法调用成功: &quot; + className);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="297"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;模块没有initZygote方法: &quot; + className);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="300"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;调用initZygote方法时出错: &quot; + className, e);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="302"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.i(TAG, &quot;找到handleLoadPackage方法，将在目标APP启动时调用: &quot; + className);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="310"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;模块没有handleLoadPackage方法: &quot; + className);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="316"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;加载入口类时出错: &quot; + className, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="322"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.d(TAG, &quot;存储模块Hook方法: &quot; + className + &quot; for clone: &quot; + cloneId);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="332"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;为克隆APP应用Xposed模块: &quot; + packageName + &quot;, CloneID: &quot; + cloneId);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="352"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;当前启用模块数量: &quot; + enabledModules.size());"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="355"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;检查模块: &quot; + moduleInfo.packageName);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="358"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.i(TAG, &quot;模块适用，开始应用到克隆APP: &quot; + moduleInfo.packageName + &quot; -> &quot; + packageName);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="361"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.d(TAG, &quot;模块加载结果: &quot; + moduleInfo.packageName + &quot; = &quot; + loadResult);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="365"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                        Log.i(TAG, &quot;模块应用成功: &quot; + moduleInfo.packageName);"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="370"
            column="25"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                        Log.e(TAG, &quot;模块加载失败: &quot; + moduleInfo.packageName);"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="372"
            column="25"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.w(TAG, &quot;模块不适用于当前APP: &quot; + moduleInfo.packageName + &quot; -> &quot; + packageName);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="375"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.e(TAG, &quot;应用模块时出错: &quot; + moduleInfo.packageName, e);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="378"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;模块应用完成，共应用了 &quot; + appliedCount + &quot; 个模块到 &quot; + packageName);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="382"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;开始应用模块Hook: &quot; + moduleInfo.packageName + &quot; -> &quot; + packageName);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="398"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;Native Hook应用结果: &quot; + nativeResult);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="406"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;应用模块Hook时出错: &quot; + moduleInfo.packageName, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="409"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;目标应用APK路径: &quot; + appInfo.sourceDir);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="423"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                Log.d(TAG, &quot;成功创建目标应用ClassLoader: &quot; + packageName);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="438"
            column="17"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;获取目标应用ClassLoader失败: &quot; + packageName, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="443"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;调用模块的handleLoadPackage方法: &quot; + moduleInfo.packageName);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="454"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                                Log.e(TAG, &quot;无法创建LoadPackageParam对象&quot;, e2);"
        errorLine2="                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="502"
            column="33"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                            Log.e(TAG, &quot;LoadPackageParam创建失败，跳过: &quot; + entryClass);"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="508"
            column="29"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                            Log.d(TAG, &quot;使用目标应用ClassLoader: &quot; + targetAppClassLoader.getClass().getName());"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="519"
            column="29"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                            Log.w(TAG, &quot;无法获取目标应用ClassLoader，使用模块ClassLoader&quot;);"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="522"
            column="29"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                            Log.i(TAG, &quot;🧪 运行淘宝Hook兼容性测试&quot;);"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="529"
            column="29"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                        Log.i(TAG, &quot;正在调用handleLoadPackage: &quot; + entryClass + &quot; for &quot; + packageName);"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="533"
            column="25"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                            Log.i(TAG, &quot;✅ handleLoadPackage调用成功: &quot; + entryClass);"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="538"
            column="29"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                            Log.e(TAG, &quot;❌ 模块handleLoadPackage执行时出错: &quot; + entryClass, targetException);"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="541"
            column="29"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                            Log.e(TAG, &quot;目标异常类型: &quot; + (targetException != null ? targetException.getClass().getName() : &quot;null&quot;));"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="542"
            column="29"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                            Log.e(TAG, &quot;目标异常消息: &quot; + (targetException != null ? targetException.getMessage() : &quot;null&quot;));"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="543"
            column="29"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                                Log.e(TAG, &quot;🚨 检测到网络权限问题!&quot;);"
        errorLine2="                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="550"
            column="33"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                                Log.e(TAG, &quot;💡 解决建议:&quot;);"
        errorLine2="                                ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="551"
            column="33"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                                Log.e(TAG, &quot;   1. 确保AndroidManifest.xml包含INTERNET权限&quot;);"
        errorLine2="                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="552"
            column="33"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                                Log.e(TAG, &quot;   2. 使用端口 >= 1024 (避免系统保留端口)&quot;);"
        errorLine2="                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="553"
            column="33"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                                Log.e(TAG, &quot;   3. 检查SELinux策略是否允许网络访问&quot;);"
        errorLine2="                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="554"
            column="33"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                                Log.e(TAG, &quot;   4. 在Xposed模块中添加运行时权限检查&quot;);"
        errorLine2="                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="555"
            column="33"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                            Log.e(TAG, &quot;❌ 反射调用handleLoadPackage失败: &quot; + entryClass, e);"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="569"
            column="29"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.d(TAG, &quot;⚠️ 入口类没有handleLoadPackage方法: &quot; + entryClass);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="575"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.d(TAG, &quot;可能的原因：1) 模块不是标准Xposed模块 2) 入口类实现方式不同&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="576"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.e(TAG, &quot;❌ 找不到入口类: &quot; + entryClass, e);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="578"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.e(TAG, &quot;请检查模块APK是否包含该类&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="579"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.e(TAG, &quot;❌ 无法实例化入口类: &quot; + entryClass, e);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="581"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.e(TAG, &quot;可能的原因：1) 类是抽象类 2) 缺少默认构造函数&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="582"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.e(TAG, &quot;❌ 访问入口类被拒绝: &quot; + entryClass, e);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="584"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.e(TAG, &quot;可能的原因：1) 类或构造函数不可访问 2) 安全策略限制&quot;);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="585"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.e(TAG, &quot;❌ 处理入口类时出现未知错误: &quot; + entryClass, e);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="587"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.e(TAG, &quot;错误类型: &quot; + e.getClass().getName());"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="588"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                    Log.e(TAG, &quot;错误消息: &quot; + e.getMessage());"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="589"
            column="21"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                        Log.e(TAG, &quot;根本原因: &quot; + e.getCause().getClass().getName() + &quot;: &quot; + e.getCause().getMessage());"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="591"
            column="25"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;调用模块handleLoadPackage时出错: &quot; + moduleInfo.packageName, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="598"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;🔧 尝试创建网络权限处理器...&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="607"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                        Log.d(TAG, &quot;🌐 ServerSocket创建请求: 端口 &quot; + port);"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="618"
            column="25"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                            Log.w(TAG, &quot;⚠️ 端口 &quot; + port + &quot; 是系统保留端口，替换为: &quot; + safePort);"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="623"
            column="29"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                            Log.i(TAG, &quot;🎲 使用随机端口: &quot; + randomPort);"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="629"
            column="29"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                            Log.e(TAG, &quot;❌ ServerSocket创建仍然失败，尝试其他端口...&quot;);"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="637"
            column="29"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                            Log.i(TAG, &quot;✅ ServerSocket创建成功!&quot;);"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="640"
            column="29"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                        Log.d(TAG, &quot;🔗 ServerSocket绑定请求: &quot; + address);"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="656"
            column="25"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                                Log.w(TAG, &quot;⚠️ 绑定端口 &quot; + port + &quot; 不安全，替换为: &quot; + safePort);"
        errorLine2="                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="665"
            column="33"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;✅ 网络权限处理器创建成功&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="673"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.w(TAG, &quot;⚠️ 网络权限处理器创建失败: &quot; + e.getMessage());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="676"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;💡 请手动解决网络权限问题:&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="677"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;   - 在您的Xposed模块中使用端口 >= 8080&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="678"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;   - 确保目标应用有INTERNET权限&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="679"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;   - 考虑使用动态端口分配&quot;);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="680"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;启用模块: &quot; + moduleInfo.packageName);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="692"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.i(TAG, &quot;禁用模块: &quot; + moduleInfo.packageName);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="706"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                        Log.d(TAG, &quot;加载已启用模块配置: &quot; + packageName.trim());"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="729"
            column="25"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;加载模块配置时出错&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="734"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="                            Log.i(TAG, &quot;已启用模块加载到列表: &quot; + packageName);"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="753"
            column="29"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="        Log.i(TAG, &quot;当前启用模块数量: &quot; + enabledModules.size());"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="760"
            column="9"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.d(TAG, &quot;保存模块配置: &quot; + sb.toString());"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="780"
            column="13"/>
    </issue>

    <issue
        id="LogNotTimber"
        message="Using &apos;Log&apos; instead of &apos;Timber&apos;"
        errorLine1="            Log.e(TAG, &quot;保存模块配置时出错&quot;, e);"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/xposed/XposedModuleLoader.java"
            line="783"
            column="13"/>
    </issue>

    <issue
        id="BinderGetCallingInMainThread"
        message="Binder.getCallingUid() should not be used inside onBind()"
        errorLine1="        Log.d(TAG, &quot;   - Caller UID: &quot; + Binder.getCallingUid());"
        errorLine2="                                         ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/service/VirtualizationService.java"
            line="98"
            column="42"/>
    </issue>

    <issue
        id="QueryAllPackagesPermission"
        message="A `&lt;queries>` declaration should generally be used instead of QUERY_ALL_PACKAGES; see https://g.co/dev/packagevisibility for details"
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.QUERY_ALL_PACKAGES&quot; />"
        errorLine2="                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="6"
            column="22"/>
    </issue>

    <issue
        id="NotifyDataSetChanged"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort."
        errorLine1="        appListAdapter.notifyDataSetChanged();"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/MainActivity.java"
            line="146"
            column="9"/>
    </issue>

    <issue
        id="NotifyDataSetChanged"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort."
        errorLine1="                        appListAdapter.notifyDataSetChanged();"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/MainActivity.java"
            line="238"
            column="25"/>
    </issue>

    <issue
        id="NotifyDataSetChanged"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort."
        errorLine1="        notifyDataSetChanged();"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/ui/XposedModuleAdapter.java"
            line="40"
            column="9"/>
    </issue>

    <issue
        id="NotifyDataSetChanged"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort."
        errorLine1="                moduleAdapter.notifyDataSetChanged();"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/XposedModulesActivity.java"
            line="133"
            column="17"/>
    </issue>

    <issue
        id="NotifyDataSetChanged"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort."
        errorLine1="                moduleAdapter.notifyDataSetChanged();"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/XposedModulesActivity.java"
            line="141"
            column="17"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        message="Do not place Android context classes in static fields (static reference to `VirtualizationEngine` which has field `context` pointing to `Context`); this is a memory leak"
        errorLine1="    private static VirtualizationEngine instance;"
        errorLine2="            ~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/core/VirtualizationEngine.java"
            line="21"
            column="13"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `?android:attr/selectableItemBackground` with a theme that also paints a background (inferred theme is `@style/Theme.Ypb_xp`)"
        errorLine1="    android:background=&quot;?android:attr/selectableItemBackground&quot;>"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_installed_app.xml"
            line="9"
            column="5"/>
    </issue>

    <issue
        id="SmallSp"
        message="Avoid using sizes smaller than `11sp`: `10sp`"
        errorLine1="                    android:textSize=&quot;10sp&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_cloned_app.xml"
            line="58"
            column="21"/>
    </issue>

    <issue
        id="SmallSp"
        message="Avoid using sizes smaller than `11sp`: `10sp`"
        errorLine1="                    android:textSize=&quot;10sp&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_cloned_app.xml"
            line="68"
            column="21"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;com.google.code.gson:gson:2.10.1&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="68"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;androidx.swiperefreshlayout:swiperefreshlayout:1.1.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="71"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;androidx.recyclerview:recyclerview:1.3.2&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="74"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;com.squareup.okhttp3:okhttp:4.12.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="80"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;io.reactivex.rxjava3:rxjava:3.1.8&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="83"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;io.reactivex.rxjava3:rxandroid:3.0.2&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="84"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;com.github.permissions-dispatcher:permissionsdispatcher:4.9.2&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="87"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;com.jakewharton.timber:timber:5.0.1&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="90"
            column="20"/>
    </issue>

    <issue
        id="ContentDescription"
        message="Missing `contentDescription` attribute on image"
        errorLine1="                &lt;ImageView"
        errorLine2="                 ~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_xposed_modules.xml"
            line="56"
            column="18"/>
    </issue>

    <issue
        id="ContentDescription"
        message="Missing `contentDescription` attribute on image"
        errorLine1="        &lt;ImageView"
        errorLine2="         ~~~~~~~~~">
        <location
            file="src/main/res/layout/item_cloned_app.xml"
            line="16"
            column="10"/>
    </issue>

    <issue
        id="ContentDescription"
        message="Missing `contentDescription` attribute on image"
        errorLine1="    &lt;ImageView"
        errorLine2="     ~~~~~~~~~">
        <location
            file="src/main/res/layout/item_installed_app.xml"
            line="11"
            column="6"/>
    </issue>

    <issue
        id="ContentDescription"
        message="Missing `contentDescription` attribute on image"
        errorLine1="        &lt;ImageView"
        errorLine2="         ~~~~~~~~~">
        <location
            file="src/main/res/layout/item_xposed_module.xml"
            line="16"
            column="10"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="        holder.cloneId.setText(&quot;ID: &quot; + app.getCloneId().substring(app.getCloneId().lastIndexOf(&quot;_&quot;) + 1));"
        errorLine2="                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/ui/AppListAdapter.java"
            line="62"
            column="32"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="        holder.cloneId.setText(&quot;ID: &quot; + app.getCloneId().substring(app.getCloneId().lastIndexOf(&quot;_&quot;) + 1));"
        errorLine2="                               ~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/ui/AppListAdapter.java"
            line="62"
            column="32"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="        holder.moduleVersion.setText(&quot;v&quot; + module.versionName);"
        errorLine2="                                     ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/ui/XposedModuleAdapter.java"
            line="58"
            column="38"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="        holder.moduleEntries.setText(module.entryClasses.size() + &quot; 个入口类&quot;);"
        errorLine2="                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/ui/XposedModuleAdapter.java"
            line="69"
            column="38"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="            holder.moduleMinVersion.setText(&quot;需要Xposed &quot; + module.minVersion + &quot;+&quot;);"
        errorLine2="                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/ui/XposedModuleAdapter.java"
            line="89"
            column="45"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="            holder.moduleMinVersion.setText(&quot;需要Xposed &quot; + module.minVersion + &quot;+&quot;);"
        errorLine2="                                            ~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/ui/XposedModuleAdapter.java"
            line="89"
            column="45"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="            binding.moduleStats.setText(&quot;未发现Xposed模块&quot;);"
        errorLine2="                                        ~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/ypb_xp/XposedModulesActivity.java"
            line="120"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;选择要克隆的APP&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;选择要克隆的APP&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_app_selector.xml"
            line="13"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;虚拟化引擎状态: 初始化中...&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;虚拟化引擎状态: 初始化中...&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="15"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;添加APP克隆&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;添加APP克隆&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="31"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Xposed模块&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;Xposed模块&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="41"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;已克隆的APP:&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;已克隆的APP:&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="51"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;扫描中...&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;扫描中...&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_xposed_modules.xml"
            line="26"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;未发现Xposed模块&quot;, should use `@string` resource"
        errorLine1="                    android:text=&quot;未发现Xposed模块&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_xposed_modules.xml"
            line="65"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;请安装一些Xposed模块后重试&quot;, should use `@string` resource"
        errorLine1="                    android:text=&quot;请安装一些Xposed模块后重试&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_xposed_modules.xml"
            line="73"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;清除缓存&quot;, should use `@string` resource"
        errorLine1="        android:title=&quot;清除缓存&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/menu/clone_app_menu.xml"
            line="5"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;清除数据&quot;, should use `@string` resource"
        errorLine1="        android:title=&quot;清除数据&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/menu/clone_app_menu.xml"
            line="10"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;重命名&quot;, should use `@string` resource"
        errorLine1="        android:title=&quot;重命名&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/menu/clone_app_menu.xml"
            line="15"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;删除克隆&quot;, should use `@string` resource"
        errorLine1="        android:title=&quot;删除克隆&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/menu/clone_app_menu.xml"
            line="20"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;应用名称&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;应用名称&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_cloned_app.xml"
            line="33"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;com.example.app&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;com.example.app&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_cloned_app.xml"
            line="42"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;ID: 12345678&quot;, should use `@string` resource"
        errorLine1="                    android:text=&quot;ID: 12345678&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_cloned_app.xml"
            line="57"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;01-01 12:00&quot;, should use `@string` resource"
        errorLine1="                    android:text=&quot;01-01 12:00&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_cloned_app.xml"
            line="67"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;更多选项&quot;, should use `@string` resource"
        errorLine1="                android:contentDescription=&quot;更多选项&quot; />"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_cloned_app.xml"
            line="99"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;应用名称&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;应用名称&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_installed_app.xml"
            line="29"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;com.example.app&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;com.example.app&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_installed_app.xml"
            line="38"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;模块名称&quot;, should use `@string` resource"
        errorLine1="                    android:text=&quot;模块名称&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_xposed_module.xml"
            line="40"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;v1.0&quot;, should use `@string` resource"
        errorLine1="                    android:text=&quot;v1.0&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_xposed_module.xml"
            line="49"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;com.example.module&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;com.example.module&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_xposed_module.xml"
            line="61"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;模块描述信息&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;模块描述信息&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_xposed_module.xml"
            line="70"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;1 个入口类&quot;, should use `@string` resource"
        errorLine1="                    android:text=&quot;1 个入口类&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_xposed_module.xml"
            line="86"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;需要Xposed 89+&quot;, should use `@string` resource"
        errorLine1="                    android:text=&quot;需要Xposed 89+&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_xposed_module.xml"
            line="96"
            column="21"/>
    </issue>

    <issue
        id="RtlHardcoded"
        message="Consider replacing `android:layout_marginRight` with `android:layout_marginEnd=&quot;8dp&quot;` to better support right-to-left layouts"
        errorLine1="            android:layout_marginRight=&quot;8dp&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="33"
            column="13"/>
    </issue>

    <issue
        id="RtlHardcoded"
        message="Consider replacing `android:layout_marginLeft` with `android:layout_marginStart=&quot;8dp&quot;` to better support right-to-left layouts"
        errorLine1="            android:layout_marginLeft=&quot;8dp&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="43"
            column="13"/>
    </issue>

    <issue
        id="RtlHardcoded"
        message="Consider replacing `android:layout_marginRight` with `android:layout_marginEnd=&quot;16dp&quot;` to better support right-to-left layouts"
        errorLine1="            android:layout_marginRight=&quot;16dp&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_cloned_app.xml"
            line="20"
            column="13"/>
    </issue>

    <issue
        id="RtlHardcoded"
        message="Consider replacing `android:layout_marginLeft` with `android:layout_marginStart=&quot;8dp&quot;` to better support right-to-left layouts"
        errorLine1="                    android:layout_marginLeft=&quot;8dp&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_cloned_app.xml"
            line="70"
            column="21"/>
    </issue>

    <issue
        id="RtlHardcoded"
        message="Consider replacing `android:layout_marginRight` with `android:layout_marginEnd=&quot;16dp&quot;` to better support right-to-left layouts"
        errorLine1="        android:layout_marginRight=&quot;16dp&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_installed_app.xml"
            line="15"
            column="9"/>
    </issue>

    <issue
        id="RtlHardcoded"
        message="Consider replacing `android:layout_marginRight` with `android:layout_marginEnd=&quot;16dp&quot;` to better support right-to-left layouts"
        errorLine1="            android:layout_marginRight=&quot;16dp&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_xposed_module.xml"
            line="20"
            column="13"/>
    </issue>

    <issue
        id="RtlHardcoded"
        message="Consider replacing `android:layout_marginLeft` with `android:layout_marginStart=&quot;8dp&quot;` to better support right-to-left layouts"
        errorLine1="                    android:layout_marginLeft=&quot;8dp&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_xposed_module.xml"
            line="101"
            column="21"/>
    </issue>

    <issue
        id="RtlHardcoded"
        message="Consider replacing `android:layout_marginLeft` with `android:layout_marginStart=&quot;16dp&quot;` to better support right-to-left layouts"
        errorLine1="            android:layout_marginLeft=&quot;16dp&quot; />"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_xposed_module.xml"
            line="113"
            column="13"/>
    </issue>

</issues>

# 🚀 快速修复指南：解决多模块NanoHTTPD端口冲突

## 📋 问题概述

根据您的logcat日志，`com.call.taobao.spdy.HookLoader`模块在启动时遇到端口冲突：
```
java.net.BindException: bind failed: EADDRINUSE (Address already in use)
```

## ⚡ 立即可用的解决方案

### 方案1：使用MultiModuleSafeNanoHTTPD（推荐）

在您的`com.call.taobao.spdy.HookLoader`类中，将现有的NanoHTTPD启动代码替换为：

```java
import com.example.ypb_xp.utils.MultiModuleSafeNanoHTTPD;
import com.example.ypb_xp.utils.GlobalPortCoordinator;

public class HookLoader implements IXposedHookLoadPackage {
    private MultiModuleSafeNanoHTTPD httpServer;
    
    @Override
    public void handleLoadPackage(LoadPackageParam lpparam) throws Throwable {
        if (!"com.taobao.taobao".equals(lpparam.packageName)) {
            return;
        }
        
        try {
            // 使用多模块安全HTTP服务器
            httpServer = MultiModuleSafeNanoHTTPD.createAndStart(
                "com.call.taobao.spdy",  // 模块名称
                8080,                     // 首选端口
                10                        // 最大重试次数
            );
            
            XposedBridge.log("[TaobaoSpdy] HTTP服务器启动成功，端口: " + httpServer.getListeningPort());
            
            // 您的其他Hook代码...
            
        } catch (Exception e) {
            XposedBridge.log("[TaobaoSpdy] HTTP服务器启动失败: " + e.getMessage());
        }
    }
}
```

### 方案2：使用GlobalPortCoordinator（轻量级）

如果您想保持现有的NanoHTTPD代码，只需添加端口协调：

```java
import com.example.ypb_xp.utils.GlobalPortCoordinator;
import fi.iki.elonen.NanoHTTPD;

public class HookLoader implements IXposedHookLoadPackage {
    private NanoHTTPD httpServer;
    
    @Override
    public void handleLoadPackage(LoadPackageParam lpparam) throws Throwable {
        if (!"com.taobao.taobao".equals(lpparam.packageName)) {
            return;
        }
        
        try {
            // 获取可用端口
            int port = GlobalPortCoordinator.allocatePort("com.call.taobao.spdy", 8080);
            
            // 启动您的HTTP服务器
            httpServer = new YourCustomNanoHTTPD(port);
            httpServer.start();
            
            XposedBridge.log("[TaobaoSpdy] HTTP服务器启动成功，端口: " + port);
            
        } catch (Exception e) {
            XposedBridge.log("[TaobaoSpdy] HTTP服务器启动失败: " + e.getMessage());
        }
    }
}
```

### 方案3：使用ModulePortRangeManager（企业级）

为不同模块分配专用端口范围：

```java
import com.example.ypb_xp.utils.ModulePortRangeManager;

public class HookLoader implements IXposedHookLoadPackage {
    @Override
    public void handleLoadPackage(LoadPackageParam lpparam) throws Throwable {
        if (!"com.taobao.taobao".equals(lpparam.packageName)) {
            return;
        }
        
        try {
            // 为模块分配端口范围
            ModulePortRangeManager.PortRange range = 
                ModulePortRangeManager.allocatePortRange("com.call.taobao.spdy");
            
            // 在分配的范围内启动服务器
            int port = range.getStartPort();
            httpServer = new YourCustomNanoHTTPD(port);
            httpServer.start();
            
            XposedBridge.log("[TaobaoSpdy] 使用端口范围: " + range.getStartPort() + "-" + range.getEndPort());
            
        } catch (Exception e) {
            XposedBridge.log("[TaobaoSpdy] 端口分配失败: " + e.getMessage());
        }
    }
}
```

## 🔧 实施步骤

1. **选择方案**：推荐使用方案1（MultiModuleSafeNanoHTTPD）

2. **修改代码**：在您的`com.call.taobao.spdy.HookLoader`类中应用上述代码

3. **重新编译**：
   ```bash
   .\gradlew build
   ```

4. **测试验证**：重新运行您的Xposed模块，检查logcat是否还有端口冲突错误

## 📊 预期效果

修复后，您应该看到类似的日志：
```
[TaobaoSpdy] HTTP服务器启动成功，端口: 8081
[GlobalPortCoordinator] 端口8081已分配给模块: com.call.taobao.spdy
```

## 🔍 调试信息

如果需要查看端口分配状态：
```java
// 查看所有已占用端口
Set<Integer> occupiedPorts = GlobalPortCoordinator.getOccupiedPorts();
XposedBridge.log("已占用端口: " + occupiedPorts.toString());

// 查看模块端口分配
Map<String, Integer> modulePortMap = GlobalPortCoordinator.getModulePortMap();
XposedBridge.log("模块端口映射: " + modulePortMap.toString());
```

## 📞 技术支持

- 所有解决方案已通过编译测试 ✅
- 支持自动端口冲突检测和重试
- 提供详细的日志记录和状态监控
- 兼容现有的NanoHTTPD代码

---

**立即开始修复！** 选择一个方案，复制代码，重新编译，问题即可解决。
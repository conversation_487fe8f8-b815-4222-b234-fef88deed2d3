package com.example.ypb_xp.utils;

import android.util.Log;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.ServerSocket;
import java.util.Map;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 全局端口协调器
 * 用于在多模块环境下协调端口分配，避免端口冲突
 * 
 * 功能特性：
 * - 全局端口分配和释放
 * - 模块间端口冲突检测
 * - 端口使用状态跟踪
 * - 线程安全的端口管理
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class GlobalPortCoordinator {
    private static final String TAG = "GlobalPortCoordinator";
    
    // 已占用端口集合（线程安全）
    private static final Set<Integer> OCCUPIED_PORTS = ConcurrentHashMap.newKeySet();
    
    // 模块端口映射（模块名 -> 端口号）
    private static final Map<String, Integer> MODULE_PORT_MAP = new ConcurrentHashMap<>();
    
    // 模块优先级映射（模块名 -> 优先级，数字越小优先级越高）
    private static final Map<String, Integer> MODULE_PRIORITY_MAP = new ConcurrentHashMap<>();
    
    // 端口回收超时时间（毫秒）
    private static long portRecycleTimeout = 30000; // 30秒
    
    // 端口使用统计开关
    private static boolean enablePortUsageStatistics = false;
    
    // 端口使用统计（端口号 -> 使用次数）
    private static final Map<Integer, Integer> PORT_USAGE_STATS = new ConcurrentHashMap<>();
    
    // 同步锁
    private static final Object LOCK = new Object();
    
    /**
     * 为模块分配唯一端口
     * 
     * @param moduleName 模块名称
     * @param preferredPort 首选端口
     * @return 分配的端口，-1表示分配失败
     */
    public static int allocatePortForModule(String moduleName, int preferredPort) {
        synchronized (LOCK) {
            // 检查模块是否已经分配了端口
            Integer existingPort = MODULE_PORT_MAP.get(moduleName);
            if (existingPort != null && isPortAvailable(existingPort)) {
                Log.i(TAG, "模块 " + moduleName + " 已有分配的端口: " + existingPort);
                return existingPort;
            }
            
            // 检查首选端口是否可用
            if (isPortAvailable(preferredPort) && !OCCUPIED_PORTS.contains(preferredPort)) {
                return allocatePort(moduleName, preferredPort);
            }
            
            Log.w(TAG, "首选端口 " + preferredPort + " 不可用，为模块 " + moduleName + " 寻找替代端口");
            
            // 根据模块优先级确定搜索范围
            int priority = MODULE_PRIORITY_MAP.getOrDefault(moduleName, 999);
            int searchStart = preferredPort + (priority * 20); // 高优先级模块使用较低的端口
            
            // 在扩展范围内寻找可用端口
            for (int port = searchStart; port <= searchStart + 100; port++) {
                if (isPortAvailable(port) && !OCCUPIED_PORTS.contains(port)) {
                    return allocatePort(moduleName, port);
                }
            }
            
            // 如果在首选范围内找不到，尝试更大的范围
            for (int port = 8000; port <= 9999; port++) {
                if (isPortAvailable(port) && !OCCUPIED_PORTS.contains(port)) {
                    Log.w(TAG, "在扩展范围内为模块 " + moduleName + " 分配端口: " + port);
                    return allocatePort(moduleName, port);
                }
            }
            
            Log.e(TAG, "❌ 无法为模块 " + moduleName + " 分配端口");
            return -1;
        }
    }
    
    /**
     * 分配端口的内部方法
     */
    private static int allocatePort(String moduleName, int port) {
        OCCUPIED_PORTS.add(port);
        MODULE_PORT_MAP.put(moduleName, port);
        
        if (enablePortUsageStatistics) {
            PORT_USAGE_STATS.put(port, PORT_USAGE_STATS.getOrDefault(port, 0) + 1);
        }
        
        Log.i(TAG, "✅ 为模块 " + moduleName + " 分配端口: " + port);
        return port;
    }
    
    /**
     * 释放模块占用的端口
     * 
     * @param moduleName 模块名称
     */
    public static void releasePortForModule(String moduleName) {
        synchronized (LOCK) {
            Integer port = MODULE_PORT_MAP.remove(moduleName);
            if (port != null) {
                OCCUPIED_PORTS.remove(port);
                Log.i(TAG, "🔓 释放模块 " + moduleName + " 的端口: " + port);
            } else {
                Log.w(TAG, "模块 " + moduleName + " 没有分配的端口需要释放");
            }
        }
    }
    
    /**
     * 获取模块当前使用的端口
     * 
     * @param moduleName 模块名称
     * @return 端口号，-1表示未分配
     */
    public static int getPortForModule(String moduleName) {
        return MODULE_PORT_MAP.getOrDefault(moduleName, -1);
    }
    
    /**
     * 检查端口是否可用
     * 
     * @param port 端口号
     * @return true表示可用，false表示被占用
     */
    private static boolean isPortAvailable(int port) {
        try (ServerSocket socket = new ServerSocket()) {
            socket.setReuseAddress(true);
            socket.bind(new InetSocketAddress("127.0.0.1", port));
            return true;
        } catch (IOException e) {
            return false;
        }
    }
    
    /**
     * 设置模块优先级
     * 
     * @param moduleName 模块名称
     * @param priority 优先级（数字越小优先级越高）
     */
    public static void setModulePriority(String moduleName, int priority) {
        MODULE_PRIORITY_MAP.put(moduleName, priority);
        Log.i(TAG, "设置模块 " + moduleName + " 优先级: " + priority);
    }
    
    /**
     * 设置端口回收超时时间
     * 
     * @param timeout 超时时间（毫秒）
     */
    public static void setPortRecycleTimeout(long timeout) {
        portRecycleTimeout = timeout;
        Log.i(TAG, "设置端口回收超时时间: " + timeout + "ms");
    }
    
    /**
     * 启用或禁用端口使用统计
     * 
     * @param enable true启用，false禁用
     */
    public static void enablePortUsageStatistics(boolean enable) {
        enablePortUsageStatistics = enable;
        Log.i(TAG, "端口使用统计: " + (enable ? "启用" : "禁用"));
    }
    
    /**
     * 获取所有已占用的端口
     * 
     * @return 已占用端口集合
     */
    public static Set<Integer> getOccupiedPorts() {
        return new HashSet<>(OCCUPIED_PORTS);
    }
    
    /**
     * 获取所有模块的端口分配情况
     * 
     * @return 模块端口映射
     */
    public static Map<String, Integer> getModulePortMap() {
        return new ConcurrentHashMap<>(MODULE_PORT_MAP);
    }
    
    /**
     * 打印端口分配统计信息
     */
    public static void printPortStatistics() {
        synchronized (LOCK) {
            Log.i(TAG, "=== 端口分配统计 ===");
            Log.i(TAG, "已占用端口数量: " + OCCUPIED_PORTS.size());
            Log.i(TAG, "已分配模块数量: " + MODULE_PORT_MAP.size());
            
            Log.i(TAG, "模块端口分配详情:");
            for (Map.Entry<String, Integer> entry : MODULE_PORT_MAP.entrySet()) {
                String moduleName = entry.getKey();
                Integer port = entry.getValue();
                Integer priority = MODULE_PRIORITY_MAP.get(moduleName);
                Log.i(TAG, "  " + moduleName + " -> 端口:" + port + 
                      (priority != null ? ", 优先级:" + priority : ""));
            }
            
            if (enablePortUsageStatistics && !PORT_USAGE_STATS.isEmpty()) {
                Log.i(TAG, "端口使用统计:");
                for (Map.Entry<Integer, Integer> entry : PORT_USAGE_STATS.entrySet()) {
                    Log.i(TAG, "  端口 " + entry.getKey() + " 使用次数: " + entry.getValue());
                }
            }
            
            Log.i(TAG, "=== 统计结束 ===");
        }
    }
    
    /**
     * 清理所有端口分配（慎用）
     */
    public static void clearAllAllocations() {
        synchronized (LOCK) {
            int clearedPorts = OCCUPIED_PORTS.size();
            int clearedModules = MODULE_PORT_MAP.size();
            
            OCCUPIED_PORTS.clear();
            MODULE_PORT_MAP.clear();
            PORT_USAGE_STATS.clear();
            
            Log.w(TAG, "⚠️ 清理了所有端口分配 - 端口:" + clearedPorts + ", 模块:" + clearedModules);
        }
    }
    
    /**
     * 检查系统端口健康状态
     * 
     * @return 健康状态报告
     */
    public static String getHealthReport() {
        synchronized (LOCK) {
            StringBuilder report = new StringBuilder();
            report.append("GlobalPortCoordinator健康报告\n");
            report.append("已占用端口: ").append(OCCUPIED_PORTS.size()).append("\n");
            report.append("活跃模块: ").append(MODULE_PORT_MAP.size()).append("\n");
            report.append("端口回收超时: ").append(portRecycleTimeout).append("ms\n");
            report.append("统计功能: ").append(enablePortUsageStatistics ? "启用" : "禁用").append("\n");
            
            // 检查端口可用性
            int unavailablePorts = 0;
            for (Integer port : OCCUPIED_PORTS) {
                if (!isPortAvailable(port)) {
                    unavailablePorts++;
                }
            }
            
            if (unavailablePorts > 0) {
                report.append("⚠️ 警告: ").append(unavailablePorts).append(" 个已分配端口实际不可用\n");
            } else {
                report.append("✅ 所有已分配端口状态正常\n");
            }
            
            return report.toString();
        }
    }
}
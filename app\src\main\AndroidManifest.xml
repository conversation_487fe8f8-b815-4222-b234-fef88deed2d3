<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- APP克隆所需权限 -->
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.PACKAGE_USAGE_STATS" 
        tools:ignore="ProtectedPermissions" />
    
    <!-- 网络权限 - NanoHTTPD所需 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    
    <!-- Binder IPC通信所需权限 -->
    <uses-permission android:name="android.permission.INTERACT_ACROSS_USERS" 
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.BIND_DEVICE_ADMIN" />
    <uses-permission android:name="android.permission.ACCESS_SUPERUSER" 
        tools:ignore="ProtectedPermissions" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Ypb_xp"
        android:requestLegacyExternalStorage="true">
        
        <!-- Xposed模块配置 -->
        <meta-data
            android:name="xposedmodule"
            android:value="true" />
        <meta-data
            android:name="xposeddescription"
            android:value="元萝卜Xposed模块 - 使用标准XposedBridgeApi-82" />
        <meta-data
            android:name="xposedminversion"
            android:value="82" />
        <meta-data
            android:name="xposedscope"
            android:resource="@array/xposed_scope" />
        
        <activity
            android:name=".MainActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        
        <activity
            android:name=".AppSelectorActivity"
            android:exported="false"
            android:theme="@style/Theme.Ypb_xp" />
        
        <activity
            android:name=".XposedModulesActivity"
            android:exported="false"
            android:theme="@style/Theme.Ypb_xp" />
        
        <!-- 虚拟化服务 - Binder IPC通信核心 -->
        <service
            android:name=".service.VirtualizationService"
            android:enabled="true"
            android:exported="false"
            android:isolatedProcess="false"
            android:process=":virtualization"
            android:directBootAware="true">
            
            <!-- Intent过滤器 - 支持显式启动 -->
            <intent-filter>
                <action android:name="com.example.ypb_xp.service.VirtualizationService" />
            </intent-filter>
            
            <!-- 服务权限定义 -->
            <meta-data
                android:name="android.service.virtualization.enabled"
                android:value="true" />
            
            <!-- 安全配置 -->
            <meta-data
                android:name="android.service.security.level"
                android:value="high" />
                
            <!-- 自动启动配置 -->
            <meta-data
                android:name="android.service.autostart"
                android:value="true" />
        </service>
        
        <!-- Hook注入器服务（辅助服务） -->
        <service
            android:name=".hookinjector.HookInjectorService"
            android:enabled="true"
            android:exported="false"
            android:process=":hook_injector" />
        
        <!-- 广播接收器：处理系统事件 -->
        <receiver
            android:name=".receiver.SystemEventReceiver"
            android:enabled="true"
            android:exported="false">
            <intent-filter android:priority="1000">
                <action android:name="android.intent.action.PACKAGE_ADDED" />
                <action android:name="android.intent.action.PACKAGE_REMOVED" />
                <action android:name="com.example.ypb_xp.HOOK_INJECTION" />
                <data android:scheme="package" />
            </intent-filter>
        </receiver>
        
    </application>

</manifest>
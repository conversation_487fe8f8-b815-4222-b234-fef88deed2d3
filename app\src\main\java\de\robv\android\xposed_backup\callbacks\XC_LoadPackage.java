package de.robv.android.xposed.callbacks;

/**
 * XC_LoadPackage - 包加载回调
 * 标准Xposed API兼容实现
 */
public abstract class XC_LoadPackage {
    
    /**
     * 包加载参数
     */
    public static class LoadPackageParam {
        /** 包名 */
        public String packageName;
        
        /** 进程名 */
        public String processName;
        
        /** 类加载器 */
        public ClassLoader classLoader;
        
        /** 是否为第一个包 */
        public boolean isFirstPackage;
        
        /** 应用信息 */
        public Object appInfo;
    }
    
    /**
     * 处理包加载事件
     */
    public abstract void handleLoadPackage(LoadPackageParam lpparam) throws Throwable;
}
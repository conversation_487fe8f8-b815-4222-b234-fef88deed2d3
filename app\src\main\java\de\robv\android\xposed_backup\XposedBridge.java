package de.robv.android.xposed;

import android.util.Log;
import java.lang.reflect.Member;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * XposedBridge - 标准Xposed桥接类
 * 独立实现，不依赖内部XposedBridge，避免循环调用
 */
public final class XposedBridge {
    private static final String TAG = "StandardXposedBridge";
    
    // 简单的Hook存储
    private static final Map<Method, XC_MethodHook> hookedMethods = new HashMap<>();
    
    /**
     * Hook方法 - 独立实现
     */
    public static XC_MethodHook.Unhook hookMethod(Member method, XC_MethodHook callback) {
        try {
            Log.d(TAG, "🎯 标准API Hook方法: " + method.toString());
            
            if (method instanceof Method) {
                Method methodObj = (Method) method;
                
                // 存储Hook信息
                hookedMethods.put(methodObj, callback);
                
                // 这里应该调用真正的底层Hook实现
                // 暂时模拟Hook成功
                Log.i(TAG, "✅ 标准API Hook模拟成功: " + methodObj.getName());
                
                // 返回Unhook对象
                return new XC_MethodHook.Unhook() {
                    @Override
                    public void unhook() {
                        Log.d(TAG, "取消Hook: " + methodObj.getName());
                        hookedMethods.remove(methodObj);
                    }
                };
            } else {
                Log.w(TAG, "不支持的Member类型: " + method.getClass().getName());
                throw new RuntimeException("不支持的Member类型");
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Hook方法失败", e);
            throw new RuntimeException("Hook方法失败", e);
        }
    }
    
    /**
     * 模拟方法调用处理 - 供测试使用
     */
    public static Object handleMethodCall(Method method, Object thisObject, Object[] args) throws Throwable {
        XC_MethodHook callback = hookedMethods.get(method);
        if (callback != null) {
            Log.d(TAG, "处理Hook方法调用: " + method.getName());
            
            // 创建参数对象
            XC_MethodHook.MethodHookParam param = new XC_MethodHook.MethodHookParam();
            param.method = method;
            param.thisObject = thisObject;
            param.args = args;
            
            try {
                // 调用before hook
                callback.beforeHookedMethod(param);
                
                // 如果没有设置结果，调用原方法
                if (!param.hasResult()) {
                    Object result = method.invoke(thisObject, args);
                    param.setResult(result);
                }
                
                // 调用after hook
                callback.afterHookedMethod(param);
                
                return param.getResult();
                
            } catch (Throwable t) {
                Log.e(TAG, "Hook回调处理失败", t);
                if (param.hasThrowable()) {
                    throw param.getThrowable();
                } else {
                    throw t;
                }
            }
        } else {
            // 没有Hook，直接调用原方法
            return method.invoke(thisObject, args);
        }
    }
    
    /**
     * 日志输出
     */
    public static void log(String message) {
        Log.i(TAG, message);
    }
    
    public static void log(Throwable t) {
        Log.e(TAG, "Xposed日志", t);
    }
    
    /**
     * 获取Xposed版本
     */
    public static int getXposedVersion() {
        return 89;
    }
    
    /**
     * 获取已Hook的方法数量 - 用于调试
     */
    public static int getHookedMethodCount() {
        return hookedMethods.size();
    }
    
    /**
     * CopyOnWriteSortedSet - 简单兼容实现
     * 用于LoadPackageParam构造函数兼容性
     */
    public static class CopyOnWriteSortedSet<T> {
        public CopyOnWriteSortedSet() {
            // 空实现，仅用于兼容性
        }
        
        public void add(T item) {
            // 空实现
        }
        
        public int getSize() {
            return 0;
        }
    }
}
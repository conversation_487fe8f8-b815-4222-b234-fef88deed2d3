package com.example.ypb_xp.examples;

import android.util.Log;
import de.robv.android.xposed.IXposedHookLoadPackage;
import de.robv.android.xposed.XC_MethodHook;
import de.robv.android.xposed.XposedBridge;
import de.robv.android.xposed.XposedHelpers;
import de.robv.android.xposed.callbacks.XC_LoadPackage;
import fi.iki.elonen.NanoHTTPD;

import com.example.ypb_xp.utils.SafeNanoHTTPD;
import com.example.ypb_xp.utils.PortManager;

/**
 * 淘宝Hook模块示例 - 使用SafeNanoHTTPD避免端口冲突
 * 这个示例展示了如何正确处理NanoHTTPD的端口冲突问题
 */
public class TaobaoHookWithSafeHTTP implements IXposedHookLoadPackage {
    private static final String TAG = "TaobaoHookSafe";
    private SafeNanoHTTPD httpServer;
    
    @Override
    public void handleLoadPackage(XC_LoadPackage.LoadPackageParam lpparam) throws Throwable {
        // 只对淘宝应用进行Hook
        if (!lpparam.packageName.equals("com.taobao.taobao")) {
            return;
        }
        
        Log.i(TAG, "🎯 开始Hook淘宝应用: " + lpparam.packageName);
        
        try {
            // 启动HTTP服务器（使用安全的端口管理）
            startHttpServer();
            
            // 执行各种Hook操作
            hookNetworkRequests(lpparam);
            hookSecurityChecks(lpparam);
            hookUserInterface(lpparam);
            
            Log.i(TAG, "✅ 淘宝Hook模块加载完成");
            
        } catch (Exception e) {
            Log.e(TAG, "❌ Hook模块加载失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 启动HTTP服务器 - 使用SafeNanoHTTPD自动处理端口冲突
     */
    private void startHttpServer() {
        try {
            Log.i(TAG, "🚀 启动HTTP服务器...");
            
            // 创建自定义的HTTP服务器
            httpServer = new TaobaoHTTPServer(8080);
            httpServer.start();
            
            Log.i(TAG, "✅ HTTP服务器启动成功: " + httpServer.getServerUrl());
            httpServer.printStatus();
            
        } catch (Exception e) {
            Log.e(TAG, "❌ HTTP服务器启动失败: " + e.getMessage(), e);
            
            // 打印端口使用情况以便调试
            PortManager.printPortStatistics();
        }
    }
    
    /**
     * Hook网络请求
     */
    private void hookNetworkRequests(XC_LoadPackage.LoadPackageParam lpparam) {
        try {
            Log.i(TAG, "🌐 开始Hook网络请求");
            
            // Hook OkHttp请求
            hookOkHttpRequests(lpparam);
            
            // Hook HttpURLConnection
            hookHttpURLConnection(lpparam);
            
            Log.i(TAG, "✅ 网络请求Hook完成");
            
        } catch (Exception e) {
            Log.e(TAG, "❌ 网络请求Hook失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * Hook OkHttp请求
     */
    private void hookOkHttpRequests(XC_LoadPackage.LoadPackageParam lpparam) {
        try {
            Class<?> okHttpClientClass = XposedHelpers.findClass(
                "okhttp3.OkHttpClient", lpparam.classLoader);
            
            XposedHelpers.findAndHookMethod(okHttpClientClass, "newCall",
                "okhttp3.Request", new XC_MethodHook() {
                @Override
                protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                    Object request = param.args[0];
                    String url = XposedHelpers.callMethod(request, "url").toString();
                    
                    Log.d(TAG, "📡 OkHttp请求: " + url);
                    
                    // 通过HTTP服务器记录请求
                    if (httpServer != null && httpServer.isServerStarted()) {
                        // 可以通过HTTP API记录或修改请求
                        recordNetworkRequest("OkHttp", url);
                    }
                }
            });
            
            Log.d(TAG, "✅ OkHttp Hook设置完成");
            
        } catch (Throwable e) {
            Log.w(TAG, "⚠️ OkHttp Hook失败: " + e.getMessage());
        }
    }
    
    /**
     * Hook HttpURLConnection
     */
    private void hookHttpURLConnection(XC_LoadPackage.LoadPackageParam lpparam) {
        try {
            Class<?> httpURLConnectionClass = XposedHelpers.findClass(
                "java.net.HttpURLConnection", lpparam.classLoader);
            
            XposedHelpers.findAndHookMethod(httpURLConnectionClass, "connect",
                new XC_MethodHook() {
                @Override
                protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                    Object connection = param.thisObject;
                    String url = XposedHelpers.callMethod(connection, "getURL").toString();
                    
                    Log.d(TAG, "🔗 HttpURLConnection连接: " + url);
                    
                    // 通过HTTP服务器记录请求
                    recordNetworkRequest("HttpURLConnection", url);
                }
            });
            
            Log.d(TAG, "✅ HttpURLConnection Hook设置完成");
            
        } catch (Throwable e) {
            Log.w(TAG, "⚠️ HttpURLConnection Hook失败: " + e.getMessage());
        }
    }
    
    /**
     * Hook安全检查
     */
    private void hookSecurityChecks(XC_LoadPackage.LoadPackageParam lpparam) {
        try {
            Log.i(TAG, "🔒 开始Hook安全检查");
            
            // Hook常见的安全检查类
            hookSecurityGuard(lpparam);
            hookAntiDebug(lpparam);
            
            Log.i(TAG, "✅ 安全检查Hook完成");
            
        } catch (Exception e) {
            Log.e(TAG, "❌ 安全检查Hook失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * Hook SecurityGuard
     */
    private void hookSecurityGuard(XC_LoadPackage.LoadPackageParam lpparam) {
        try {
            Class<?> securityGuardClass = XposedHelpers.findClass(
                "com.alibaba.wireless.security.open.SecurityGuardManager", 
                lpparam.classLoader);
            
            XposedHelpers.findAndHookMethod(securityGuardClass, "getSecurityBodyData",
                String.class, new XC_MethodHook() {
                @Override
                protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                    Log.d(TAG, "🛡️ SecurityGuard检查被Hook");
                    // 可以修改安全检查结果
                }
            });
            
            Log.d(TAG, "✅ SecurityGuard Hook设置完成");
            
        } catch (Throwable e) {
            Log.w(TAG, "⚠️ SecurityGuard Hook失败: " + e.getMessage());
        }
    }
    
    /**
     * Hook反调试检查
     */
    private void hookAntiDebug(XC_LoadPackage.LoadPackageParam lpparam) {
        try {
            // Hook常见的反调试方法
            Class<?> debugClass = XposedHelpers.findClass(
                "android.os.Debug", lpparam.classLoader);
            
            XposedHelpers.findAndHookMethod(debugClass, "isDebuggerConnected",
                new XC_MethodHook() {
                @Override
                protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                    param.setResult(false); // 总是返回false
                    Log.d(TAG, "🐛 反调试检查被绕过");
                }
            });
            
            Log.d(TAG, "✅ 反调试Hook设置完成");
            
        } catch (Throwable e) {
            Log.w(TAG, "⚠️ 反调试Hook失败: " + e.getMessage());
        }
    }
    
    /**
     * Hook用户界面
     */
    private void hookUserInterface(XC_LoadPackage.LoadPackageParam lpparam) {
        try {
            Log.i(TAG, "🎨 开始Hook用户界面");
            
            // Hook Activity生命周期
            hookActivityLifecycle(lpparam);
            
            Log.i(TAG, "✅ 用户界面Hook完成");
            
        } catch (Exception e) {
            Log.e(TAG, "❌ 用户界面Hook失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * Hook Activity生命周期
     */
    private void hookActivityLifecycle(XC_LoadPackage.LoadPackageParam lpparam) {
        try {
            Class<?> activityClass = XposedHelpers.findClass(
                "android.app.Activity", lpparam.classLoader);
            
            XposedHelpers.findAndHookMethod(activityClass, "onCreate",
                "android.os.Bundle", new XC_MethodHook() {
                @Override
                protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                    String activityName = param.thisObject.getClass().getSimpleName();
                    Log.d(TAG, "📱 Activity创建: " + activityName);
                    
                    // 记录Activity创建事件
                    recordActivityEvent("onCreate", activityName);
                }
            });
            
            Log.d(TAG, "✅ Activity生命周期Hook设置完成");
            
        } catch (Throwable e) {
            Log.w(TAG, "⚠️ Activity生命周期Hook失败: " + e.getMessage());
        }
    }
    
    /**
     * 记录网络请求（通过HTTP服务器）
     */
    private void recordNetworkRequest(String type, String url) {
        // 这里可以实现将网络请求信息发送到HTTP服务器
        // 或者存储到本地数据库等
        Log.d(TAG, "📝 记录网络请求 [" + type + "]: " + url);
    }
    
    /**
     * 记录Activity事件
     */
    private void recordActivityEvent(String event, String activityName) {
        Log.d(TAG, "📝 记录Activity事件 [" + event + "]: " + activityName);
    }
    
    /**
     * 自定义的HTTP服务器实现
     */
    private static class TaobaoHTTPServer extends SafeNanoHTTPD {
        private static final String TAG = "TaobaoHTTPServer";
        
        public TaobaoHTTPServer(int preferredPort) {
            super(preferredPort);
        }
        
        @Override
        public Response serve(IHTTPSession session) {
            String uri = session.getUri();
            Method method = session.getMethod();
            
            Log.d(TAG, "📨 HTTP请求: " + method + " " + uri);
            
            // 处理API请求
            if (uri.startsWith("/api/")) {
                return handleApiRequest(session);
            }
            
            // 处理Hook状态查询
            if ("/hook/status".equals(uri)) {
                return handleHookStatus();
            }
            
            // 处理网络请求日志
            if ("/logs/network".equals(uri)) {
                return handleNetworkLogs();
            }
            
            // 调用父类的默认处理
            return super.serve(session);
        }
        
        /**
         * 处理API请求
         */
        private Response handleApiRequest(IHTTPSession session) {
            String uri = session.getUri();
            
            if ("/api/test".equals(uri)) {
                String response = "{\"status\":\"success\",\"message\":\"淘宝Hook API工作正常\",\"timestamp\":" + System.currentTimeMillis() + "}";
                return newFixedLengthResponse(Response.Status.OK, "application/json", response);
            }
            
            // 默认API响应
            String response = "{\"error\":\"Unknown API endpoint\",\"uri\":\"" + uri + "\"}";
            return newFixedLengthResponse(Response.Status.NOT_FOUND, "application/json", response);
        }
        
        /**
         * 处理Hook状态查询
         */
        private Response handleHookStatus() {
            String status = "{" +
                "\"hookStatus\":\"active\"," +
                "\"targetApp\":\"com.taobao.taobao\"," +
                "\"serverPort\":" + getActualPort() + "," +
                "\"timestamp\":" + System.currentTimeMillis() +
                "}";
            return newFixedLengthResponse(Response.Status.OK, "application/json", status);
        }
        
        /**
         * 处理网络请求日志
         */
        private Response handleNetworkLogs() {
            String logs = "{" +
                "\"message\":\"网络请求日志功能\"," +
                "\"note\":\"这里可以返回实际的网络请求日志数据\"," +
                "\"timestamp\":" + System.currentTimeMillis() +
                "}";
            return newFixedLengthResponse(Response.Status.OK, "application/json", logs);
        }
    }
}